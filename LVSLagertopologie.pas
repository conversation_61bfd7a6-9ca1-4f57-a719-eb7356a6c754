﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : LVSLagertopologie
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/LVSLagertopologie.pas $
// $Revision: 119 $
// $Modtime: 2.04.24 22:27 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Funktion f�r die Pflege der Lager-Datenstrukturen
//*****************************************************************************
unit LVSLagertopologie;

interface

uses Classes, StdCtrls;

function CreateMandant        (const RefProject : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String; var Ref : Integer) : Integer;
function CreateSubMandant     (const RefMasterMand : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String; var Ref : Integer) : Integer;
function CopyMandant          (const SourceRef : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const CopyACL : Boolean; var Ref : Integer) : Integer;
function ChangeMandant        (const MandantRef : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String) : Integer;
function DeleteMandant        (const MandantRef : Integer) : Integer;
function ActivateMandant      (const MandantRef : Integer) : Integer;
function InactivateMandant    (const MandantRef : Integer) : Integer;
function DefMandantLager      (const RefMand, RefLager : Integer; const Status : Char) : Integer;
function DefMandantLocation   (const RefMand, RefLocation : Integer; const Status : Char) : Integer;
function DefMandantAdr            (const RefMand : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
function DefMandantLocationAdr    (const RefMand, RefLoc : Integer; const AdrText, AdrAddText, RoadText, PLZText, OrtText, LandText, ContactText, PhoneText, MailText: String) : Integer;
function DefMandantAbsAdr         (const RefMand, RefLoc : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer; overload;
function DefMandantAbsAdr         (const RefMand, RefLoc : Integer; const AdrText, AddText, RoadText, PLZText, OrtText, LandText, ContactText, PhoneText, MailText: String) : Integer; overload;
function SetMandantDefaultQS      (const RefMand : Integer; const RefQSDefWE, RefQSDefRET, RefQSDefWA, RefQSDefKOMM : Integer) : Integer;
function SetMandantAvisierung     (const RefMand : Integer; const AvisMailAdr, AvisOpt: String) : Integer;
function SetMandantBioKontrollNr  (const RefMand : Integer; const KontrollNr : String) : Integer;
function SetMandantStandartHints  (const RefMand : Integer; const PickHint, PackHint : String) : Integer;
function SetMandantReturnHint     (const RefMand : Integer; const ReturnHint : String) : Integer;
function SetMandantStammdaten     (const RefMand : Integer; const Manager, Contact, InfoMail, URL, TaxNr, VAtId, HRNr: String) : Integer;
function SetMandantBank           (const RefMand : Integer; const Bank, IBAN, BIC : String) : Integer;
function SetMandantLogo           (const RefMand : Integer; const LogoName : String) : Integer;
function SetMandantLS_DATUM_MAX_TAGE (const RefMand : Integer; const Tage: Integer) : Integer;
function SetMandantEORI           (const RefMand : Integer; const EORINr : String) : Integer;
function SetMandantCurrency       (const RefMand : Integer; const Currency : String) : Integer;
function SetMandantInvOption      (const RefMand : Integer; const InvOpt : String) : Integer;
function SetMandantInvCheckOption (const RefMand : Integer; const InvOpt : String) : Integer;
function InsertMandantBesMandant  (const RefMand : Integer; const RefBesMan : Integer; const RefLager : Integer) : Integer;
function ClearMandantBesMandant   (const RefMand : Integer; const RefLager : Integer) : Integer;

function SetMandantArtikelConfig (const RefMand : Integer; const ArtikelSuffix : String; const ArtikelNext, ArtikelStart, ArtikelEnd, ArtikelLen : Integer) : Integer; overload;
function SetMandantArtikelConfig (const RefMand : Integer; const ArtikelPrefix, ArtikelSuffix : String; const ArtikelNext, ArtikelStart, ArtikelEnd, ArtikelLen : Integer) : Integer; overload;

function SetMandantZollDaten     (const RefMand : Integer; const OptAutoDeclaration : Char; const BaseTricNr : String) : Integer;
function SetMandantPackTypeGroup (const RefMand : Integer; const PackGroup : String) : Integer;

function GetNextMandantArtikelNr (const RefMand : Integer; var ArtikelNr : String) : Integer;

function SetKommunikationsPartner (const Mandant, Lager, Vorgang, Partner : String; const ActiveFlag : Boolean) : Integer;

function CreateLocation     (const RefProject : Integer; const LocationName, Beschreibung, IFCID : String; var Ref : Integer) : Integer;
function CopyLocation       (const OrigRef : Integer; const LocationName, Beschreibung, IFCID : String; var Ref : Integer) : Integer;
function ChangeLocation     (const LocationRef : Integer; const LocationName, Beschreibung, IFCID : String) : Integer;
function SetLocationAdresse (const LocationRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
function SetLocationInfos   (const LocationRef : Integer; const Telefon, Fax, Contact, MailAdr : String) : Integer;
function SetLocationLogin   (const LocationRef, MaxSessions : Integer) : Integer;
function SetLocationOptions (const LocationRef : Integer; const InVisCol, SetOpt, ConfigOpt : String) : Integer;
function SetLocationBioKontrollNr (const LocationRef : Integer; const KontrollNr : String) : Integer;
function SetLocationConfig  (const LocationRef : Integer; const ConfigSet : String) : Integer;

function CreateLager     (const RefProject : Integer; const LocationName, LagerName, Beschreibung, LagerArt, BetriebNr, ILN, EUNr, KommOpt, Options : String; const RefSperrLager, HKL : Integer; var Ref : Integer) : Integer;

function CopyLager       (const SourceRef : Integer; const LocationName, LagerName, Beschreibung : String; const CopyACO, CopyStruct, CopyZuordung : Boolean; var Ref : Integer) : Integer; overload;
function CopyLager       (const SourceRef : Integer; const LocationName, LagerName, Beschreibung, BetriebsNr, ILN : String; const CopyOptions : String; var Ref : Integer) : Integer; overload;

function ChangeLager     (const LagerRef, LocationRef, RefSperrlager, HKL : Integer; const LagerName, Beschreibung, BetriebNr, ILN, EUNr, KommOpt, Options : String) : Integer;
function ChangeLPBereich (const LPRef, LBRef, LBZoneRef : Integer) : Integer;
function SetLagerAdresse (const LagerRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
function SetLagerInfos   (const LagerRef : Integer; const Telefon, Fax, Contact, MailAdr : String) : Integer;
function SetLagerIFCID   (const LagerRef : Integer; const IFCID : String) : Integer;

function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer) : Integer; overload;
function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer; const AutoInvFlag : Boolean) : Integer; overload;
function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer; const AutoInvFlag, AutoInvArFlag : Boolean) : Integer; overload;

function SetLagerNaheNull   (const LagerRef : Integer; const Menge : Integer) : Integer;
function SetLagerOpeBestand (const LagerRef : Integer; const OptBestand : Boolean) : Integer;

function CreateDepot     (const RefLoc, RefMand, RefSped : Integer; const DepotName, Beschreibung, SpedKennung, DepotNr, KurzBez, BetriebNr, ILN, EUNr, KommOpt, AnsprechP, Telefon : String; var Ref : Integer) : Integer;
function ChangeDepot     (const DepotRef, RefLoc, RefMand, RefSped : Integer; const DepotName, Beschreibung, SpedKennung, DepotNr, KurzBez, BetriebNr, ILN, EUNr, KommOpt, AnsprechP, Telefon : String) : Integer;
function SetDepotAdresse (const DepotRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
function DeleteDepot     (const DepotRef : Integer) : Integer;

function CreateLB     (const RefLager : Integer;
                       const LBName, Bezeichnung, ShortName, Art : String;
                       const UseForVerrat, UseForKomm, AutoLEDel : Boolean;
                       const RBGName : String;
                       const KommArt : String;
                       const KommAblauf : String;
                       const KommName : String;
                       const KommFolge, KommFreigabe : Integer;
                       const StoreOpt, KommOpt, WEOpt : String;
                       const KommGrpRef : Integer;
                       const AutoLERef : Integer;
                       const AutoEin : Char;
                       const ABCKlasse : String;
                       const PrioEin, MaxDiffAr : Integer;
                       var   Ref : Integer) : Integer;

function ChangeLB     (const LBRef : Integer;
                       const LBName, Bezeichnung, ShortName, Art : String;
                       const UseForVerrat, UseForKomm, AutoLEDel : Boolean;
                       const RBGName : String;
                       const KommArt : String;
                       const KommAblauf : String;
                       const KommName : String;
                       const KommFolge, KommFreigabe : Integer;
                       const StoreOpt, KommOpt, WEOpt : String;
                       const KommGrpRef : Integer;
                       const AutoLERef : Integer;
                       const AutoEin : Char;
                       const ABCKlasse : String;
                       const PrioEin, MaxDiffAr : Integer) : Integer;

function CreateLBZone (const LBRef, KommFolge : Integer;
                       const LBZoneName, LBZoneNummer, Bezeichnung : String;
                       const ABCKlasse : String;
                       var   Ref : Integer) : Integer;

function ChangeLBZone (const LBZoneRef, KommFolge : Integer;
                       const LBZoneName, LBZoneNummer, Bezeichnung : String;
                       const ABCKlasse : String) : Integer;

function DeleteLBZone (const LBZoneRef : Integer) : Integer;

function ChangeLBNachschub (const LBRef : Integer;
                            const Art : String;
                            const MHDReinFlag, AutoFlag : Char;
                            const MinFaktor : Integer;
                            const MinEinheit : String;
                            const MaxFaktor : Integer;
                            const MaxEinheit : String;
                            const CrossUpdate : Boolean) : Integer;

function SetLBACO         (const RefLB : Integer; const RefACO : Integer) : Integer;
function SetLBACOBestand  (const RefLB : Integer; const RefACO : Integer) : Integer;

function SetLBZoll   (const RefLB : Integer; const ZollOpt : Boolean) : Integer;
function SetLBMixCategory (const RefLB : Integer; const MixCategoryOpt : Boolean) : Integer;
function SetLBStoreSelection (const RefLB : Integer; const LEAbstellenOpt : Char) : Integer;
function SetLBShowLP (const RefLB : Integer; const OptShowLP : Char) : Integer;
function SetTransportTypeAssignLB (const RefLB, RefTransType : Integer; const Flag : Boolean) : Integer;
function SetLBNachschubBereich    (const RefLB, RefNachLB, Prio : Integer; const Flag : Boolean) : Integer;

function DeleteLB     (const RefLB : Integer) : Integer;

function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr      : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer; overload;

function CreateLagerplatz (LBRef,
                           LBZoneRef      : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           ABC        : String;
                           Scanable   : TCheckBoxState;
                           RBGKoor,
                           WWSKoor    : String;
                           var RefLP  : Integer) : Integer;  overload;

function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer; overload;

function CreateLagerplatz (LBRef, LBZoneRef : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt     : Integer;
                           L, B, H      : Integer;
                           LTAnz,
                           MaxGewicht   : Integer;
                           FolgeNr      : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor      : String;
                           var RefLP    : Integer) : Integer; overload;

function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr    : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer; overload;

function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr    : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String;
                           var RefLP : Integer) : Integer; overload;

function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL        : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor    : String) : Integer; overload;

function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;

function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                ABC          : String;
                                Scanable     : TCheckBoxState;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String;
                                var RefLP    : Integer) : Integer; overload;

function ChangeLagerplatz (Ref        : Integer;
                           UpdKey     : Integer;
                           LBRef      : Integer;
                           LBZoneRef  : Integer;
                           PlatzNr    : String;
                           StellplatzNr : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer; overload;

function ChangeLagerplatz (Ref        : Integer;
                           UpdKey     : Integer;
                           LBRef      : Integer;
                           LBZoneRef  : Integer;
                           PlatzNr    : String;
                           StellplatzNr : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor      : String) : Integer; overload;

function ChangeLagerplatz (Ref        : Integer;
                           UpdKey     : Integer;
                           LBRef      : Integer;
                           LBZoneRef  : Integer;
                           PlatzNr    : String;
                           StellplatzNr : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           ABC          : String;
                           Scanable     : TCheckBoxState;
                           RBGKoor,
                           WWSKoor      : String) : Integer; overload;

function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL        : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor    : String) : Integer; overload;

function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;

function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                ABC          : String;
                                Scanable     : TCheckBoxState;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;

function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL : Integer) : Integer; overload;

function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL, StabelFaktor : Integer) : Integer; overload;

function ChangeLagerplatz (const Ref,
                           UpdKey,
                           LBRef            : Integer;
                           const PlatzName  : String;
                           const RefLPArt   : Integer;
                           const L, B, H    : Integer;
                           const LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL,
                           StabelFaktor   : Integer;
                           const ABC      : String;
                           const Scanable : TCheckBoxState
                           ) : Integer; overload;

function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL        : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean) : Integer; overload;

function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL        : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean;
                                StellNrStart : Integer) : Integer; overload;

function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStep,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL        : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean;
                                StellNrStart : Integer) : Integer; overload;

function CreateLagerplatzSerie (LBRef, LBZoneRef, RegalRef : Integer;
                                SerienName : String;
                                Reihe      : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                FachNr,
                                FachNrStep  : Integer;
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd,
                                EbeneStep  : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL        : Integer;
                                PlatzNrStart : Integer;
                                StellNrStart : Integer;
                                Options      : String) : Integer; overload;

function CreateFachplatzSerie  (LBRef, LBZoneRef      : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                EbeneStart,
                                EbeneEnd,
                                L, B, H   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean
                                ) : Integer; overload;

function CreateFachplatzSerie  (LBRef, LBZoneRef      : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                EbeneStep,
                                EbeneStart,
                                EbeneEnd,
                                L, B, H   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean
                                ) : Integer; overload;

function SperrenLagerplatz  (const Ref : Integer; const Grund : String) : Integer;
function FreigabeLagerplatz (const Ref : Integer) : Integer;

function DeleteLagerplatz (const Ref : Integer) : Integer;

function CreateKommPlatz           (const RefLager, RefLP : Integer) : Integer; overload;
function CreateKommPlatz           (const RefLager, RefLP : Integer; var RefKommLP : Integer) : Integer; overload;
function SetKommPlatzEigenschaften (const RefKommLP : Integer; const FolgeNr, MaxArtikle : Integer) : Integer;
function SetKommNachschubPlatz     (const RefKommLP, RefNachLP : Integer) : Integer;
function DeleteKommPlatz           (const RefKommLP : Integer) : Integer;

function SetLPFolgeNr     (const LPRef, FolgeNr : Integer) : Integer;
function SetLPTypenDaten   (const LPRef : Integer; const Flag : Boolean) : Integer; overload;
function SetLPTypenDaten   (const LPRef : Integer; const BesFlag, MixFlag : Boolean) : Integer; overload;
function SetLPTypenOptions (const LPRef : Integer; const Options : String) : Integer;

function SetLPLTZuordnung  (const LPRef, LTRef : Integer; const Flag : Boolean) : Integer;
function SetLTMandantUse   (const LTRef, RefMand : Integer; const Flag : Boolean) : Integer;

function SetFolgeNummern  (const RefLB : Integer; const Reihe : string;
                           const PlatzVon,PlatzBis,TiefeVon,TiefeBis,EbeneVon,EbeneBis : Integer;
                           const Art : String;
                           const Start, Step : Integer;
                           const ChangeLPNr : Boolean) : Integer;

function ChangeRegal (const RefRegal, RefGasse : Integer; const Bezeichnung : String; const FachLast, EbeneLast, FeldLast : Integer) : Integer;

function Set_LB_INV(const RefLB: Integer;
                    const Status: String;
                    const IntervalDays: Integer;
                    const MaxLpCount: Integer;
                    const MinLpAge: Integer) : Integer;

function Set_LB_RUECK(const RefLB: Integer;
                      const RefMHDLB: Integer;
                      const Status: String;
                      const Fuellgrad: Integer;
                      const FreeLpCount: Integer) : Integer;

implementation

uses SysUtils, DB, ADODB, DatenModul, Variants, LVSGlobalDaten, LVSSecurity;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLB (const RefLager : Integer;
                   const LBName, Bezeichnung, ShortName, Art : String;
                   const UseForVerrat, UseForKomm, AutoLEDel : Boolean;
                   const RBGName : String;
                   const KommArt : String;
                   const KommAblauf : String;
                   const KommName : String;
                   const KommFolge, KommFreigabe : Integer;
                   const StoreOpt, KommOpt, WEOpt : String;
                   const KommGrpRef : Integer;
                   const AutoLERef : Integer;
                   const AutoEin : Char;
                   const ABCKlasse : String;
                   const PrioEin, MaxDiffAr : Integer;
                   var   Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CREATE_LB';
    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, LBName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Bezeichnung);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pArt',ftString,pdInput, 8, Art);

    if (UseForVerrat) then
      Parameters.CreateParameter('pUseForVerrat',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pUseForVerrat',ftString,pdInput, 1, '0');

    if (UseForKomm) then
      Parameters.CreateParameter('pUseForKomm',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pUseForKomm',ftString,pdInput, 1, '0');

    if (AutoLEDel) then
      Parameters.CreateParameter('pAutoLEDel',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAutoLEDel',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pRBGName',ftString,pdInput, 64, RBGName);

    Parameters.CreateParameter('pKommArt',ftString,pdInput, 8, KommArt);
    Parameters.CreateParameter('pKommAblauf',ftString,pdInput, 8, KommAblauf);

    Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, GetPLSQLParameter (KommFolge));
    Parameters.CreateParameter('pKommFreigabe',ftInteger,pdInput, 12, GetPLSQLParameter (KommFreigabe));

    Parameters.CreateParameter('pKommName',ftString,pdInput, 32, KommName);
    Parameters.CreateParameter('pEinlagerOpt',ftString,pdInput, 32, StoreOpt);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 16, KommOpt);
    Parameters.CreateParameter('pWEOpt',ftString,pdInput, 16, WEOpt);

    if (AutoLERef < 0) then
      Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, AutoLERef);

    if (KommGrpRef < 0) then
      Parameters.CreateParameter('pRefArKommGrp',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefArKommGrp',ftInteger,pdInput, 12, KommGrpRef);

    Parameters.CreateParameter('pAutoEinl',ftInteger,pdInput, 1, AutoEin);

    if (PrioEin < 0) then
      Parameters.CreateParameter('pEinlagerPrio',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEinlagerPrio',ftInteger,pdInput, 12, PrioEin);

    if (MaxDiffAr < 0) then
      Parameters.CreateParameter('pMaxDiffAr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pMaxDiffAr',ftInteger,pdInput, 12, MaxDiffAr);

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 1, copy (ABCKlasse, 1, 1));

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBACO
//* Author       : Stefan Graf
//* Datum        : 23.11.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBACO (const RefLB : Integer; const RefACO : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_LB_ACO';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pRefACO',ftInteger,pdInput, 1, GetPLSQLParameter (RefACO));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBACOBestand
//* Author       : Stefan Graf
//* Datum        : 07.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBACOBestand  (const RefLB : Integer; const RefACO : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_LB_ACO_BESTAND';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pRefACO',ftInteger,pdInput, 1, GetPLSQLParameter (RefACO));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBZoll
//* Author       : Stefan Graf
//* Datum        : 21.03.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBZoll (const RefLB : Integer; const ZollOpt : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_ZOLL_OPTION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);

    if (ZollOpt) then
      Parameters.CreateParameter('pOptZoll',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pOptZoll',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBMixCategory
//* Author       : Stefan Graf
//* Datum        : 17.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBMixCategory (const RefLB : Integer; const MixCategoryOpt : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_OPT_MIX_CATEGORY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);

    if (MixCategoryOpt) then
      Parameters.CreateParameter('pOptMixCat',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pOptMixCat',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBStoreSelection
//* Author       : Stefan Graf
//* Datum        : 17.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBStoreSelection (const RefLB : Integer; const LEAbstellenOpt : Char) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_EINLAGER_AUSWAHL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pOptLEAb',ftString,pdInput, 1, LEAbstellenOpt);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBShowLP (const RefLB : Integer; const OptShowLP : Char) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.SET_OPT_SHOW_LP_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);

    if (OptShowLP = #0) then
      Parameters.CreateParameter('pOptShowLP',ftString,pdInput, 1, NULL)
    else Parameters.CreateParameter('pOptShowLP',ftString,pdInput, 1, OptShowLP);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.06.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetTransportTypeAssignLB (const RefLB, RefTransType : Integer; const Flag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CHANGE_LB_TRANSPORT_ASSIGN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pTransRef',ftInteger,pdInput, 12, RefTransType);

    if Flag then
      Parameters.CreateParameter('pFlag',ftString,pdInput, 12, '+')
    else Parameters.CreateParameter('pFlag',ftString,pdInput, 1, '-');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLBNachschubBereich
//* Author       : Stefan Graf
//* Datum        : 29.09.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLBNachschubBereich (const RefLB, RefNachLB, Prio : Integer; const Flag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CHANGE_LB_NACHSCHUB_BEREICH';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pRefNachLB',ftInteger,pdInput, 12, RefNachLB);
    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if Flag then
      Parameters.CreateParameter('pFlag',ftString,pdInput, 12, '+')
    else Parameters.CreateParameter('pFlag',ftString,pdInput, 1, '-');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteLB (const RefLB : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.DELETE_LB';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefLB);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLB (const LBRef : Integer;
                   const LBName, Bezeichnung, ShortName, Art : String;
                   const UseForVerrat, UseForKomm, AutoLEDel : Boolean;
                   const RBGName : String;
                   const KommArt : String;
                   const KommAblauf : String;
                   const KommName : String;
                   const KommFolge, KommFreigabe : Integer;
                   const StoreOpt, KommOpt, WEOpt : String;
                   const KommGrpRef : Integer;
                   const AutoLERef : Integer;
                   const AutoEin : Char;
                   const ABCKlasse : String;
                   const PrioEin, MaxDiffAr : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CHANGE_LB';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, LBName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Bezeichnung);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pArt',ftString,pdInput, 8, Art);

    if (UseForVerrat) then
      Parameters.CreateParameter('pUseForVerrat',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pUseForVerrat',ftString,pdInput, 1, '0');

    if (UseForKomm) then
      Parameters.CreateParameter('pUseForKomm',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pUseForKomm',ftString,pdInput, 1, '0');

    if (AutoLEDel) then
      Parameters.CreateParameter('pAutoLEDel',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAutoLEDel',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pRBGName',ftString,pdInput, 64, RBGName);

    Parameters.CreateParameter('pKommArt',ftString,pdInput, 8, KommArt);
    Parameters.CreateParameter('pKommAblauf',ftString,pdInput, 8, KommAblauf);

    Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, GetPLSQLParameter (KommFolge));
    Parameters.CreateParameter('pKommFreigabe',ftInteger,pdInput, 12, GetPLSQLParameter (KommFreigabe));

    Parameters.CreateParameter('pKommName',ftString,pdInput, 32, KommName);
    Parameters.CreateParameter('pEinlagerOpt',ftString,pdInput, 32, StoreOpt);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 16, KommOpt);
    Parameters.CreateParameter('pWEOpt',ftString,pdInput, 16, WEOpt);

    if (AutoLERef < 0) then
      Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, AutoLERef);

    if (KommGrpRef < 0) then
      Parameters.CreateParameter('pRefArKommGrp',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefArKommGrp',ftInteger,pdInput, 12, KommGrpRef);

    Parameters.CreateParameter('pAutoEinl',ftInteger,pdInput, 1, AutoEin);

    if (PrioEin < 0) then
      Parameters.CreateParameter('pEinlagerPrio',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEinlagerPrio',ftInteger,pdInput, 12, PrioEin);

    if (MaxDiffAr < 0) then
      Parameters.CreateParameter('pMaxDiffAr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pMaxDiffAr',ftInteger,pdInput, 12, MaxDiffAr);

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 1, copy (ABCKlasse, 1, 1));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLBZone (const LBRef, KommFolge : Integer;
                       const LBZoneName, LBZoneNummer, Bezeichnung : String;
                       const ABCKlasse : String;
                       var   Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CREATE_LB_ZONE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, LBZoneName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Bezeichnung);
    Parameters.CreateParameter('pNummer',ftString,pdInput, 16, LBZoneNummer);
    Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, GetPLSQLParameter (KommFolge));
    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 1, copy (ABCKlasse, 1, 1));

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLBZone (const LBZoneRef, KommFolge : Integer;
                       const LBZoneName, LBZoneNummer, Bezeichnung : String;
                       const ABCKlasse : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CHANGE_LB_ZONE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('LBZoneRef',ftInteger,pdInput, 12, LBZoneRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, LBZoneName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Bezeichnung);
    Parameters.CreateParameter('pNummer',ftString,pdInput, 16, LBZoneNummer);
    Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, GetPLSQLParameter (KommFolge));
    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 1, copy (ABCKlasse, 1, 1));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteLBZone (const LBZoneRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.DELETE_LB_ZONE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('LBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLBNachschub (const LBRef : Integer;
                            const Art : String;
                            const MHDReinFlag, AutoFlag : Char;
                            const MinFaktor : Integer;
                            const MinEinheit : String;
                            const MaxFaktor : Integer;
                            const MaxEinheit : String;
                            const CrossUpdate : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LB.CHANGE_LB_NACHSCHUB';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);

    Parameters.CreateParameter('pArt',ftString,pdInput, 5, Art);

    if (MHDReinFlag = #0) then
      Parameters.CreateParameter('pMHDReinFlag',ftString,pdInput, 1, NULL)
    else Parameters.CreateParameter('pMHDReinFlag',ftString,pdInput, 1, MHDReinFlag);

    if (AutoFlag = #0) then
      Parameters.CreateParameter('pAutoFlag',ftString,pdInput, 1, NULL)
    else Parameters.CreateParameter('pAutoFlag',ftString,pdInput, 1, AutoFlag);

    if (MinFaktor < 0) then
      Parameters.CreateParameter('pMinFaktor',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pMinFaktor',ftInteger,pdInput, 12, MinFaktor);

    Parameters.CreateParameter('pMinEinheit',ftString,pdInput, 8, MinEinheit);

    if (MaxFaktor < 0) then
      Parameters.CreateParameter('pMaxFaktor',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pMaxFaktor',ftInteger,pdInput, 12, MaxFaktor);

    Parameters.CreateParameter('pMaxEinheit',ftString,pdInput, 8, MaxEinheit);

    if (CrossUpdate) then
      Parameters.CreateParameter('pCrossUpdate',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pCrossUpdate',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr    : String;
                           PlatzName  : String;
                           RegalNr    : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);

    if (Feld = -1) then
      Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, Feld);

    if (Fach = -1) then
      Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, Fach);

    if (Ebene = -1) then
      Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, Ebene);

    if (Tiefe = -1) then
      Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, Tiefe);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.05.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef,
                           LBZoneRef    : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           ABC        : String;
                           Scanable   : TCheckBoxState;
                           RBGKoor,
                           WWSKoor    : String;
                           var RefLP  : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);

    if (Feld = -1) then
      Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, Feld);

    if (Fach = -1) then
      Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, Fach);

    if (Ebene = -1) then
      Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, Ebene);

    if (Tiefe = -1) then
      Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, Tiefe);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 16, copy (StellplatzNr, 1, 16));
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, copy (PlatzName, 1, 64));
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    if (StabelFaktor = -1) then
      Parameters.CreateParameter('pStapelFaktor',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pStapelFaktor',ftInteger,pdInput, 12, StabelFaktor);

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 1, copy (ABC, 1, 1));

    if (Scanable = cbChecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 1, '1')
    else if (Scanable = cbUnchecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 1, '0')
    else
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 1, NULL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('oRefLP',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefLP := StoredProcedure.Parameters.ParamValues ['oRefLP'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer; overload;

var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Copy (RegalNr, 1, 5));

    Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, GetPLSQLParameter (Feld));
    Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, GetPLSQLParameter (Fach));
    Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, GetPLSQLParameter (Ebene));
    Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (Tiefe));

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, Copy (PlatzName, 1, 64));
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RegalNr      : String;
                           Feld,
                           Fach,
                           Ebene,
                           Tiefe,
                           RefLPArt     : Integer;
                           L, B, H      : Integer;
                           LTAnz,
                           MaxGewicht   : Integer;
                           FolgeNr      : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor      : String;
                           var RefLP    : Integer) : Integer; overload;

var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  RefLP := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Copy (RegalNr, 1, 5));

    Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, GetPLSQLParameter (Feld));
    Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, GetPLSQLParameter (Fach));
    Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, GetPLSQLParameter (Ebene));
    Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (Tiefe));

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, Copy (PlatzName, 1, 64));
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));
    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));
    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);
    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('oRefLP',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefLP := StoredProcedure.Parameters.ParamValues ['oRefLP'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr    : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.09.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatz (LBRef, LBZoneRef      : Integer;
                           PlatzNr    : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String;
                           var RefLP : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  RefLP := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('oRefLP',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefLP := StoredProcedure.Parameters.ParamValues ['oRefLP'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL        : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor    : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);

    if (Feld = -1) then
      Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, Feld);

    if (Platz = -1) then
      Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, Platz);

    if (Ebene = -1) then
      Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, Ebene);

    if (Tiefe = -1) then
      Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, Tiefe);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor    : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);

    if (Feld = -1) then
      Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, Feld);

    if (Platz = -1) then
      Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, Platz);

    if (Ebene = -1) then
      Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, Ebene);

    if (Tiefe = -1) then
      Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, Tiefe);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzKoord (LBRef, LBZoneRef      : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                RegalNr    : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                ABC          : String;
                                Scanable     : TCheckBoxState;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String;
                                var RefLP    : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);

    Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, GetPLSQLParameter (Feld));
    Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, GetPLSQLParameter (Platz));
    Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, GetPLSQLParameter (Ebene));
    Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (Tiefe));

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 5, ABC);

    if (Scanable = cbChecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '1')
    else if (Scanable = cbUnchecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '0')
    else
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, NULL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('oRefLP',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefLP := StoredProcedure.Parameters.ParamValues ['oRefLP'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           LBZoneRef  : Integer;
                           PlatzNr    : String;
                           StellplatzNr : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL        : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor    : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));
    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           LBZoneRef  : Integer;
                           PlatzNr    : String;
                           StellplatzNr : String;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht : Integer;
                           FolgeNr    : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           RBGKoor,
                           WWSKoor      : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));
    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 26.05.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef        : Integer;
                           LBZoneRef    : Integer;
                           PlatzNr      : String;
                           StellplatzNr : String;
                           PlatzName    : String;
                           RefLPArt     : Integer;
                           L, B, H      : Integer;
                           LTAnz,
                           MaxGewicht   : Integer;
                           FolgeNr      : Int64;
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           Barcode,
                           ABC          : String;
                           Scanable     : TCheckBoxState;
                           RBGKoor,
                           WWSKoor      : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));
    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 32, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 5, ABC);

    if (Scanable = cbChecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '1')
    else if (Scanable = cbUnchecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '0')
    else
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, NULL);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL          : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Reihe);

    if (Feld = -1) then
      Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, Feld);

    if (Platz = -1) then
      Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, Platz);

    if (Ebene = -1) then
      Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, Ebene);

    if (Tiefe = -1) then
      Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, Tiefe);

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 16, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pFolgeNr',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, FolgeNr);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.08.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Reihe);

    Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, GetPLSQLParameter (Feld));
    Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, GetPLSQLParameter (Platz));
    Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, GetPLSQLParameter (Ebene));
    Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (Tiefe));

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 16, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.08.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatzKoord (Ref        : Integer;
                                UpdKey     : Integer;
                                LBRef      : Integer;
                                LBZoneRef  : Integer;
                                PlatzNr    : String;
                                StellplatzNr : String;
                                Reihe      : String;
                                Feld, Platz, Ebene, Tiefe : Integer;
                                PlatzName  : String;
                                RefLPArt   : Integer;
                                L, B, H    : Integer;
                                LTAnz,
                                MaxGewicht : Integer;
                                FolgeNr    : Int64;
                                Prio,
                                HKL,
                                StabelFaktor : Integer;
                                ABC          : String;
                                Scanable     : TCheckBoxState;
                                Barcode,
                                RBGKoor,
                                WWSKoor      : String) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_KOORD';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));

    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Reihe);

    Parameters.CreateParameter('pFeld',ftInteger,pdInput, 12, GetPLSQLParameter (Feld));
    Parameters.CreateParameter('pPlatz',ftInteger,pdInput, 12, GetPLSQLParameter (Platz));
    Parameters.CreateParameter('pEbene',ftInteger,pdInput, 12, GetPLSQLParameter (Ebene));
    Parameters.CreateParameter('pTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (Tiefe));

    Parameters.CreateParameter('pNr',ftString,pdInput, 32, PlatzNr);
    Parameters.CreateParameter('pStellplatzNr',ftString,pdInput, 16, StellplatzNr);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));

    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pFolgeNr',ftFloat,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));

    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 5, ABC);

    if (Scanable = cbChecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '1')
    else if (Scanable = cbUnchecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '0')
    else
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, NULL);

    Parameters.CreateParameter('pBarcode',ftString,pdInput, 32, Barcode);

    Parameters.CreateParameter('pRBGKoor',ftString,pdInput, 32, RBGKoor);
    Parameters.CreateParameter('pWWSKoor',ftString,pdInput, 32, WWSKoor);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL        : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    if (LTAnz < 1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (Ref,
                           UpdKey,
                           LBRef      : Integer;
                           PlatzName  : String;
                           RefLPArt   : Integer;
                           L, B, H    : Integer;
                           LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL, StabelFaktor : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));
    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));
    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.01.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLagerplatz (const Ref,
                           UpdKey,
                           LBRef      : Integer;
                           const PlatzName  : String;
                           const RefLPArt   : Integer;
                           const L, B, H    : Integer;
                           const LTAnz,
                           MaxGewicht,
                           Prio,
                           HKL,
                           StabelFaktor : Integer;
                           const ABC : String;
                           const Scanable : TCheckBoxState
                           ) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pBez',ftString,pdInput, 64, PlatzName);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pL',ftInteger,pdInput, 12, GetPLSQLParameter (L));
    Parameters.CreateParameter('pB',ftInteger,pdInput, 12, GetPLSQLParameter (B));
    Parameters.CreateParameter('pH',ftInteger,pdInput, 12, GetPLSQLParameter (H));
    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));
    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pHKLKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pStabelFaktor',ftInteger,pdInput, 12, GetPLSQLParameter (StabelFaktor));
    Parameters.CreateParameter('pABCKlasse',ftString,pdInput, 5, ABC);

    if (Scanable = cbChecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '1')
    else if (Scanable = cbUnchecked) then
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, '0')
    else
      Parameters.CreateParameter('pScanabled',ftString,pdInput, 12, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL       : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, FeldStart);
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, FeldEnd);
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, FeldStep);
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, FachStart);
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, FachEnd);

    if (LVSDatenModul.DatabaseVersion > 41) then begin
      Parameters.CreateParameter('pTiefeStart',ftInteger,pdInput, 12, TiefeStart);
      Parameters.CreateParameter('pTiefeEnd',ftInteger,pdInput, 12, TiefeEnd);
    end;

    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (HKL = -1) then
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, HKL);

    if (PlatzNrStart = -1) then
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, PlatzNrStart);

    if (PlatzNrFolge) then
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL       : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean;
                                StellNrStart : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStart));
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FeldEnd));
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStep));
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, GetPLSQLParameter (FachStart));
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FachEnd));

    if (LVSDatenModul.DatabaseVersion > 41) then begin
      Parameters.CreateParameter('pTiefeStart',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeStart));
      Parameters.CreateParameter('pTiefeEnd',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeEnd));
    end;

    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (PlatzNrStart));

    if (PlatzNrFolge) then
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pStellNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (StellNrStart));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzSerie (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                TiefeStart,
                                TiefeEnd,
                                EbeneStep,
                                EbeneStart,
                                EbeneEnd   : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL       : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean;
                                StellNrStart : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStart));
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FeldEnd));
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStep));
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, GetPLSQLParameter (FachStart));
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FachEnd));
    Parameters.CreateParameter('pTiefeStart',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeStart));
    Parameters.CreateParameter('pTiefeEnd',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeEnd));
    Parameters.CreateParameter('pEbeneStep',ftInteger,pdInput, 12, EbeneStep);
    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (PlatzNrStart));

    if (PlatzNrFolge) then
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pStellNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (StellNrStart));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLagerplatzSerie (LBRef, LBZoneRef, RegalRef : Integer;
                                SerienName : String;
                                Reihe      : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                FachNr,
                                FachNrStep  : Integer;
                                TiefeStart,
                                TiefeEnd,
                                EbeneStart,
                                EbeneEnd,
                                EbeneStep  : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio,
                                HKL          : Integer;
                                PlatzNrStart : Integer;
                                StellNrStart : Integer;
                                Options      : String) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_LP_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBREf',ftInteger,pdInput, 12, LBRef);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, GetPLSQLParameter (LBZoneRef));
    Parameters.CreateParameter('pRegalRef',ftInteger,pdInput, 12, GetPLSQLParameter (RegalRef));

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Reihe);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStart));
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FeldEnd));
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, GetPLSQLParameter (FeldStep));
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, GetPLSQLParameter (FachStart));
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, GetPLSQLParameter (FachEnd));
    Parameters.CreateParameter('pFachNr',ftInteger,pdInput, 12, GetPLSQLParameter (FachNr));
    Parameters.CreateParameter('pFachNrStep',ftInteger,pdInput, 12, FachNrStep);
    Parameters.CreateParameter('pTiefeStart',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeStart));
    Parameters.CreateParameter('pTiefeEnd',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeEnd));
    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);
    Parameters.CreateParameter('pEbeneStep',ftInteger,pdInput, 12, EbeneStep);
    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, GetPLSQLParameter (RefLPArt));

    Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, GetPLSQLParameter (LTAnz));
    Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, GetPLSQLParameter (MaxGewicht));

    Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, GetPLSQLParameter (Prio));
    Parameters.CreateParameter('pKlasse',ftInteger,pdInput, 12, GetPLSQLParameter (HKL));
    Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (PlatzNrStart));

    Parameters.CreateParameter('pStellNrStart',ftInteger,pdInput, 12, GetPLSQLParameter (StellNrStart));
    Parameters.CreateParameter('pOptionen',ftString,pdInput, 8, Options);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateFachplatzSerie  (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                EbeneStart,
                                EbeneEnd,
                                L, B, H    : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio       : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_FB_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, FeldStart);
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, FeldEnd);
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, FeldStep);
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, FachStart);
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, FachEnd);
    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (PlatzNrStart = -1) then
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, PlatzNrStart);

    if (PlatzNrFolge) then
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateFachplatzSerie  (LBRef, LBZoneRef : Integer;
                                SerienName : String;
                                RegalNr    : String;
                                FeldStart,
                                FeldEnd,
                                FeldStep,
                                FachStart,
                                FachEnd,
                                EbeneStep,
                                EbeneStart,
                                EbeneEnd,
                                L, B, H    : Integer;
                                RefLPArt   : Integer;
                                LTAnz,
                                MaxGewicht,
                                Prio       : Integer;
                                PlatzNrStart : Integer;
                                PlatzNrFolge : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CREATE_FB_SERIE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBZoneRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pSerie',ftString,pdInput, 64, SerienName);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, RegalNr);
    Parameters.CreateParameter('pFeldStart',ftInteger,pdInput, 12, FeldStart);
    Parameters.CreateParameter('pFeldEnd',ftInteger,pdInput, 12, FeldEnd);
    Parameters.CreateParameter('pFeldStep',ftInteger,pdInput, 12, FeldStep);
    Parameters.CreateParameter('pFachStart',ftInteger,pdInput, 12, FachStart);
    Parameters.CreateParameter('pFachEnd',ftInteger,pdInput, 12, FachEnd);
    Parameters.CreateParameter('pEbeneStep',ftInteger,pdInput, 12, EbeneStep);
    Parameters.CreateParameter('pEbeneStart',ftInteger,pdInput, 12, EbeneStart);
    Parameters.CreateParameter('pEbeneEnd',ftInteger,pdInput, 12, EbeneEnd);

    if (L = -1) then
      Parameters.CreateParameter('pL',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pL',ftInteger,pdInput, 12, L);

    if (B = -1) then
      Parameters.CreateParameter('pB',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pB',ftInteger,pdInput, 12, B);

    if (H = -1) then
      Parameters.CreateParameter('pH',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pH',ftInteger,pdInput, 12, H);

    Parameters.CreateParameter('pRefLPArt',ftInteger,pdInput, 8, RefLPArt);

    if (LTAnz = -1) then
      Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pLTAnz',ftInteger,pdInput, 12, LTAnz);

    if (MaxGewicht = -1) then
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pMaxGewicht',ftInteger,pdInput, 12, MaxGewicht);

    if (Prio = -1) then
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPrio',ftInteger,pdInput, 12, Prio);

    if (PlatzNrStart = -1) then
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pPlatzNrStart',ftInteger,pdInput, 12, PlatzNrStart);

    if (PlatzNrFolge) then
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pPlatzNrFolge',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLager  (const LagerRef, LocationRef, RefSperrlager, HKL : Integer; const LagerName, Beschreibung, BetriebNr, ILN, EUNr, KommOpt, Options : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.CHANGE_LAGER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    if (LocationRef = -1) then
      Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, LocationRef);

    if (LVSDatenModul.DatabaseVersion >= 43) then begin
      if (RefSperrlager = -1) then
        Parameters.CreateParameter('pRefSperr',ftString,pdInput, 12, NULL)
      else Parameters.CreateParameter('pRefSperr',ftString,pdInput, 12, RefSperrlager);
    end;

    Parameters.CreateParameter('pName',ftString,pdInput, 64, copy (LagerName, 1, 64));
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, copy (Beschreibung, 1, 64));
    Parameters.CreateParameter('pBetriebNr',ftString,pdInput, 8, BetriebNr);
    Parameters.CreateParameter('pILN',ftString,pdInput, 22, ILN);
    Parameters.CreateParameter('pEUNr',ftString,pdInput, 32, EUNr);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 32, KommOpt);

    if (LVSDatenModul.DatabaseVersion > 40) then
      Parameters.CreateParameter('pOptions',ftString,pdInput, 32, Options);

    if (HKL = -1) then
      Parameters.CreateParameter('pDefHKL',ftString,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pDefHKL',ftString,pdInput, 12, HKL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerAdresse (const LagerRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_LAGER_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, AdrText);
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, RoadText);
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 12, PLZText);
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, OrtText);
    Parameters.CreateParameter('pLand',ftString,pdInput, 32, LandText);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerInfos (const LagerRef : Integer; const Telefon, Fax, Contact, MailAdr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_LAGER_INFOS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, Fax);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, Contact);
    Parameters.CreateParameter('pMail',ftString,pdInput, 64, MailAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerIFCID (const LagerRef : Integer; const IFCID : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_LAGER_ID_IFC';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pIFCID',ftString,pdInput, 32, IFCID);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLagerMaxInvDiff
//* Author       : Stefan Graf
//* Datum        : 02.02.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_MAX_KOMM_INV_DIFF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pMaxDiff',ftInteger,pdInput, 12, GetPLSQLParameter (MaxDiff));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLagerMaxInvDiff
//* Author       : Stefan Graf
//* Datum        : 18.02.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer; const AutoInvFlag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_MAX_KOMM_INV_DIFF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pMaxDiff',ftInteger,pdInput, 12, GetPLSQLParameter (MaxDiff));

    if (AutoInvFlag) then
      Parameters.CreateParameter('pAutoInvFlag',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAutoInvFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLagerMaxInvDiff
//* Author       : Stefan Graf
//* Datum        : 04.11.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerMaxInvDiff (const LagerRef : Integer; const MaxDiff : Integer; const AutoInvFlag, AutoInvArFlag : Boolean) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_MAX_KOMM_INV_DIFF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pMaxDiff',ftInteger,pdInput, 12, GetPLSQLParameter (MaxDiff));

    if (AutoInvFlag) then
      Parameters.CreateParameter('pAutoInvFlag',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAutoInvFlag',ftString,pdInput, 1, '0');

    if (AutoInvArFlag) then
      Parameters.CreateParameter('pAutoInvArFlag',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pAutoInvArFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


//******************************************************************************
//* Function Name: SetLagerNaheNull
//* Author       : Stefan Graf
//* Datum        : 07.10.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerNaheNull (const LagerRef : Integer; const Menge : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_NEAR_ZERO_INV_QTY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    Parameters.CreateParameter('pQuantity',ftInteger,pdInput, 12, GetPLSQLParameter (Menge));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLagerOpeBestand
//* Author       : Stefan Graf
//* Datum        : 25.05.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLagerOpeBestand (const LagerRef : Integer; const OptBestand : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.SET_OPT_BESTAND';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LagerRef);

    if OptBestand then
      Parameters.CreateParameter('pOptBestand',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pOptBestand',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLager (const RefProject : Integer; const LocationName, LagerName, Beschreibung, LagerArt, BetriebNr, ILN, EUNr, KommOpt, Options : String; const RefSperrLager, HKL : Integer; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.CREATE_LAGER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    if (RefProject = -1) then
      Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, RefProject);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pLocation',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, copy (LagerName, 1, 64));
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, copy (Beschreibung, 1, 64));

    if (LVSDatenModul.DatabaseVersion >= 43) then begin
      Parameters.CreateParameter('pArt',ftString,pdInput, 8, LagerArt);
    end;

    Parameters.CreateParameter('pBetriebNr',ftString,pdInput, 8, BetriebNr);
    Parameters.CreateParameter('pILN',ftString,pdInput, 22, ILN);
    Parameters.CreateParameter('pEUNr',ftString,pdInput, 32, EUNr);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 32, KommOpt);

    if (LVSDatenModul.DatabaseVersion > 40) then
      Parameters.CreateParameter('pOptions',ftString,pdInput, 32, Options);

    if (LVSDatenModul.DatabaseVersion >= 43) then begin
      if (RefSperrlager = -1) then
        Parameters.CreateParameter('pRefSperr',ftString,pdInput, 12, NULL)
      else Parameters.CreateParameter('pRefSperr',ftString,pdInput, 12, RefSperrlager);

      if (HKL = -1) then
        Parameters.CreateParameter('pDefHKL',ftString,pdInput, 12, NULL)
      else Parameters.CreateParameter('pDefHKL',ftString,pdInput, 12, HKL);
    end;

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CopyLager (const SourceRef : Integer; const LocationName, LagerName, Beschreibung : String; const CopyACO, CopyStruct, CopyZuordung : Boolean; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.COPY_LAGER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRefSource',ftString,pdInput, 32, SourceRef);
    Parameters.CreateParameter('pLocation',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, copy (LagerName, 1, 64));
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, copy (Beschreibung, 1, 64));

    if CopyACO then
      Parameters.CreateParameter('pCopyACL',ftInteger,pdInput, 1, 1)
    else
      Parameters.CreateParameter('pCopyACL',ftInteger,pdInput, 1, 0);

    if CopyStruct then
      Parameters.CreateParameter('pCopyStruct',ftInteger,pdInput, 1, 1)
    else
      Parameters.CreateParameter('pCopyStruct',ftInteger,pdInput, 1, 0);

    if CopyZuordung then
      Parameters.CreateParameter('pCopyZuordung',ftInteger,pdInput, 1, 1)
    else
      Parameters.CreateParameter('pCopyZuordung',ftInteger,pdInput, 1, 0);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 24.01.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CopyLager (const SourceRef : Integer; const LocationName, LagerName, Beschreibung, BetriebsNr, ILN : String; const CopyOptions : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LAGER.COPY_LAGER_EX';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSource',ftString,pdInput, 32, SourceRef);
    Parameters.CreateParameter('pLocation',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pName',ftString,pdInput, 64, copy (LagerName, 1, 64));
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, copy (Beschreibung, 1, 64));
    Parameters.CreateParameter('pBetriebsNr',ftString,pdInput, 12, BetriebsNr);
    Parameters.CreateParameter('pILN',ftString,pdInput, 32, ILN);

    Parameters.CreateParameter('pCopyOptions',ftString,pdInput, 8, CopyOptions);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateDepot (const RefLoc, RefMand, RefSped : Integer; const DepotName, Beschreibung, SpedKennung, DepotNr, KurzBez, BetriebNr, ILN, EUNr, KommOpt, AnsprechP, Telefon : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    if (LVSDatenModul.DatabaseVersion > 47) then
      ProcedureName := 'PA_VERSAND_VERWALTUNG.CREATE_DEPOT'
    else
      ProcedureName := 'PA_LAGER.CREATE_DEPOT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);

    Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, GetPLSQLParameter (RefLoc));
    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, GetPLSQLParameter (RefMand));
    Parameters.CreateParameter('pRefSped',ftString,pdInput, 12, GetPLSQLParameter (RefSped));

    Parameters.CreateParameter('pSpedKennung',ftString,pdInput, 16, SpedKennung);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, DepotName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Beschreibung);
    Parameters.CreateParameter('pDepotNr',ftString,pdInput, 32, DepotNr);
    Parameters.CreateParameter('pKurzBez',ftString,pdInput, 32, KurzBez);
    Parameters.CreateParameter('pBetriebNr',ftString,pdInput, 8, BetriebNr);
    Parameters.CreateParameter('pILN',ftString,pdInput, 22, ILN);
    Parameters.CreateParameter('pEUNr',ftString,pdInput, 32, EUNr);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 32, KommOpt);

    if (LVSDatenModul.DatabaseVersion >= 44) then begin
      Parameters.CreateParameter('pAnsprechP',ftString,pdInput, 64, AnsprechP);
      Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, Telefon);
    end;

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeDepot  (const DepotRef, RefLoc, RefMand, RefSped : Integer; const DepotName, Beschreibung, SpedKennung, DepotNr, KurzBez, BetriebNr, ILN, EUNr, KommOpt, AnsprechP, Telefon : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    if (LVSDatenModul.DatabaseVersion > 47) then
      ProcedureName := 'PA_VERSAND_VERWALTUNG.CHANGE_DEPOT'
    else
      ProcedureName := 'PA_LAGER.CHANGE_DEPOT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, DepotRef);

    Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, GetPLSQLParameter (RefLoc));
    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, GetPLSQLParameter (RefMand));
    Parameters.CreateParameter('pRefSped',ftString,pdInput, 12, GetPLSQLParameter (RefSped));

    Parameters.CreateParameter('pSpedKennung',ftString,pdInput, 16, SpedKennung);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, DepotName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 64, Beschreibung);
    Parameters.CreateParameter('pDepotNr',ftString,pdInput, 32, DepotNr);
    Parameters.CreateParameter('pKurzBez',ftString,pdInput, 32, KurzBez);
    Parameters.CreateParameter('pBetriebNr',ftString,pdInput, 8, BetriebNr);
    Parameters.CreateParameter('pILN',ftString,pdInput, 22, ILN);
    Parameters.CreateParameter('pEUNr',ftString,pdInput, 32, EUNr);
    Parameters.CreateParameter('pKommOpt',ftString,pdInput, 32, KommOpt);

    if (LVSDatenModul.DatabaseVersion >= 44) then begin
      Parameters.CreateParameter('pAnsprechP',ftString,pdInput, 64, AnsprechP);
      Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, Telefon);
    end;

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetDepotAdresse (const DepotRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    if (LVSDatenModul.DatabaseVersion > 47) then
      ProcedureName := 'PA_VERSAND_VERWALTUNG.SET_DEPOT_ADR'
    else
      ProcedureName := 'PA_LAGER.SET_LAGER_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, DepotRef);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, AdrText);
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, RoadText);
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 12, PLZText);
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, OrtText);
    Parameters.CreateParameter('pLand',ftString,pdInput, 8, LandText);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteDepot (const DepotRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    if (LVSDatenModul.DatabaseVersion > 47) then
      ProcedureName := 'PA_VERSAND_VERWALTUNG.DELETE_DEPOT'
    else
      ProcedureName := 'PA_LAGER.DELETE_DEPOT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, DepotRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeMandant  (const MandantRef : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.CHANGE_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, MandantRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, MandantName);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pILN',ftString,pdInput, 16, ILN);
    Parameters.CreateParameter('pERPId',ftString,pdInput, 32, ERPID);
    Parameters.CreateParameter('pDXFId',ftString,pdInput, 32, DXFID);

    if (pRefBesMand = -1) then
      Parameters.CreateParameter('pRefBesMand',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefBesMand',ftInteger,pdInput, 12, pRefBesMand);

    Parameters.CreateParameter('pConfigOpt',ftString,pdInput, 128, ConfigOpt);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteMandant (const MandantRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.DELETE_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, MandantRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ActivateMandant (const MandantRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.ACTIVATE_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, MandantRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InactivateMandant (const MandantRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.INACTIVATE_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, MandantRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateMandant (const RefProject : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.CREATE_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    if (RefProject = -1) then
      Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, RefProject);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, MandantName);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pILN',ftString,pdInput, 16, ILN);
    Parameters.CreateParameter('pERPId',ftString,pdInput, 16, ERPID);
    Parameters.CreateParameter('pDXFId',ftString,pdInput, 16, DXFID);

    if (pRefBesMand = -1) then
      Parameters.CreateParameter('pRefBesMand',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefBesMand',ftInteger,pdInput, 12, pRefBesMand);

    Parameters.CreateParameter('pConfigOpt',ftString,pdInput, 128, ConfigOpt);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateSubMandant (const RefMasterMand : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const pRefBesMand : Integer; const ConfigOpt : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.CREATE_SUB_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRefMasterMand',ftInteger,pdInput, 12, RefMasterMand);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, MandantName);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pILN',ftString,pdInput, 16, ILN);
    Parameters.CreateParameter('pERPId',ftString,pdInput, 16, ERPID);
    Parameters.CreateParameter('pDXFId',ftString,pdInput, 16, DXFID);

    Parameters.CreateParameter('pConfigOpt',ftString,pdInput, 128, ConfigOpt);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CopyMandant (const SourceRef : Integer; const MandantName, ShortName, Beschreibung, ILN, ERPID, DXFID : String; const CopyACL : Boolean; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.COPY_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRefSource',ftInteger,pdInput, 12, SourceRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, MandantName);
    Parameters.CreateParameter('pShortName',ftString,pdInput, 16, ShortName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pILN',ftString,pdInput, 16, ILN);
    Parameters.CreateParameter('pERPId',ftString,pdInput, 16, ERPID);
    Parameters.CreateParameter('pDXFId',ftString,pdInput, 16, DXFID);

    if (CopyACL) then
      Parameters.CreateParameter('pCopyACL',ftInteger,pdInput, 1, 1)
    else
      Parameters.CreateParameter('pCopyACL',ftInteger,pdInput, 1, 0);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantLager (const RefMand, RefLager : Integer; const Status : Char) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_LAGER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pStatus',ftString,pdInput, 1, Status);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DefMandantLocation
//* Author       : Stefan Graf
//* Datum        : 21.02.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantLocation (const RefMand, RefLocation : Integer; const Status : Char) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_LOCATION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLocation',ftInteger,pdInput, 12, RefLocation);
    Parameters.CreateParameter('pStatus',ftString,pdInput, 1, Status);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantDefaultQS (const RefMand : Integer; const RefQSDefWE, RefQSDefRET, RefQSDefWA, RefQSDefKOMM : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_DEFAULT_QS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);

    if (RefQSDefWE = -1) then
      Parameters.CreateParameter('pRefQSWE',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefQSWE',ftInteger,pdInput, 12, RefQSDefWE);

    if (RefQSDefRET = -1) then
      Parameters.CreateParameter('pRefQSRET',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefQSRET',ftInteger,pdInput, 12, RefQSDefRET);

    if (RefQSDefWA = -1) then
      Parameters.CreateParameter('pRefQSWA',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefQSWA',ftInteger,pdInput, 12, RefQSDefWA);

    if (RefQSDefKOMM = -1) then
      Parameters.CreateParameter('pRefQSKOMM',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefQSKOMM',ftInteger,pdInput, 12, RefQSDefKOMM);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantAdr (const RefMand : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, copy (AdrText, 1, 64));
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, copy (RoadText, 1, 64));
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 8, copy (PLZText, 1, 8));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, copy (OrtText, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 32, copy (LandText, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DefMandantAbsAdr
//* Author       : Stefan Graf
//* Datum        : 01.04.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantAbsAdr (const RefMand, RefLoc : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_ABS_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, RefLoc);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, copy (AdrText, 1, 64));
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, copy (RoadText, 1, 64));
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 8, copy (PLZText, 1, 8));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, copy (OrtText, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 32, copy (LandText, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DefMandantAbsAdr
//* Author       : Stefan Graf
//* Datum        : 19.01.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantAbsAdr (const RefMand, RefLoc : Integer; const AdrText, AddText, RoadText, PLZText, OrtText, LandText, ContactText, PhoneText, MailText: String) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_ABS_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, RefLoc);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, copy (AdrText, 1, 64));
    Parameters.CreateParameter('pAdrAdd',ftString,pdInput, 64, copy (AddText, 1, 64));
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, copy (RoadText, 1, 64));
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 8, copy (PLZText, 1, 8));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, copy (OrtText, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 32, copy (LandText, 1, 32));
    Parameters.CreateParameter('pContact',ftString,pdInput, 32, copy (ContactText, 1, 64));
    Parameters.CreateParameter('pPhone',ftString,pdInput, 32, copy (PhoneText, 1, 32));
    Parameters.CreateParameter('pMail',ftString,pdInput, 32, copy (MailText, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DefMandantLocationAdr
//* Author       : Stefan Graf
//* Datum        : 01.04.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DefMandantLocationAdr (const RefMand, RefLoc : Integer; const AdrText, AdrAddText, RoadText, PLZText, OrtText, LandText, ContactText, PhoneText, MailText : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_LOCATION_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLoc',ftString,pdInput, 12, RefLoc);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, copy (AdrText, 1, 64));
    Parameters.CreateParameter('pAdrZus',ftString,pdInput, 64, copy (AdrAddText, 1, 64));
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, copy (RoadText, 1, 64));
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 8, copy (PLZText, 1, 8));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, copy (OrtText, 1, 64));
    Parameters.CreateParameter('pLand',ftString,pdInput, 32, copy (LandText, 1, 32));
    Parameters.CreateParameter('pContact',ftString,pdInput, 32, copy (ContactText, 1, 64));
    Parameters.CreateParameter('pPhone',ftString,pdInput, 32, copy (PhoneText, 1, 32));
    Parameters.CreateParameter('pMail',ftString,pdInput, 32, copy (MailText, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantStammdaten
//* Author       : Stefan Graf
//* Datum        : 15.05.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantStammdaten (const RefMand : Integer; const Manager, Contact, InfoMail, URL, TaxNr, VATId, HRNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_STAMMDATEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pManager',ftString,pdInput, 64, copy (Manager, 1, 64));
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, copy (Contact, 1, 64));
    Parameters.CreateParameter('pInfoMail',ftString,pdInput, 64, copy (InfoMail, 1, 64));
    Parameters.CreateParameter('pURL',ftString,pdInput, 64, copy (URL, 1, 64));
    Parameters.CreateParameter('pTaxNr',ftString,pdInput, 32, copy (TaxNr, 1, 32));
    Parameters.CreateParameter('pVATId',ftString,pdInput, 32, copy (VATId, 1, 32));
    Parameters.CreateParameter('pHRNr',ftString,pdInput, 64, copy (HRNr, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantBank
//* Author       : Stefan Graf
//* Datum        : 15.05.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantBank (const RefMand : Integer; const Bank, IBAN, BIC : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_BANK';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pBank',ftString,pdInput, 64, copy (Bank, 1, 64));
    Parameters.CreateParameter('pIBAN',ftString,pdInput, 64, copy (IBAN, 1, 64));
    Parameters.CreateParameter('pBIC',ftString,pdInput, 16, copy (BIC, 1, 16));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantLS_DATUM_MAX_TAGE
//* Author       : Leon Achteresch
//* Datum        : 18.06.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantLS_DATUM_MAX_TAGE (const RefMand : Integer; const Tage: Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_OPT_LS_DATUM_MAX_TAGE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pTage',ftInteger ,pdInput, 64, Tage);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantBank
//* Author       : Stefan Graf
//* Datum        : 15.05.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantLogo (const RefMand : Integer; const LogoName : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_LOGO';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pLogo',ftString,pdInput, 128, LogoName);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantEORI
//* Author       : Stefan Graf
//* Datum        : 03.08.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantEORI (const RefMand : Integer; const EORINr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_EORI';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pEORI',ftString,pdInput, 32, EORINr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantCurrency
//* Author       : Stefan Graf
//* Datum        : 03.08.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantCurrency (const RefMand : Integer; const Currency : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_CURRENCY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);
    Parameters.CreateParameter('pCurrency',ftString,pdInput, 32, Currency);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantAvisierung
//* Author       : Stefan Graf
//* Datum        : 01.04.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantAvisierung (const RefMand : Integer; const AvisMailAdr, AvisOpt: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_AVISIERUNG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pAvisMailAdr',ftString,pdInput, 256, copy (AvisMailAdr, 1, 256));
    Parameters.CreateParameter('pAvisOpt',ftString,pdInput, 8, copy (AvisOpt, 1, 8));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantBioKontrollNr
//* Author       : Stefan Graf
//* Datum        : 12.08.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantBioKontrollNr (const RefMand : Integer; const KontrollNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_BIO_KONTROLL_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pKontrollNr',ftString,pdInput, 32, copy (KontrollNr, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantInvOption
//* Author       : Stefan Graf
//* Datum        : 30.07.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantInvOption (const RefMand : Integer; const InvOpt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_INV_OPTIONS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pInvOpt',ftString,pdInput, 128, copy (InvOpt, 1, 8));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantInvCheckOption
//* Author       : Stefan Graf
//* Datum        : 30.07.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantInvCheckOption (const RefMand : Integer; const InvOpt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_INV_CHECK_OPTIONS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pInvOpt',ftString,pdInput, 128, copy (InvOpt, 1, 8));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantStandartHints
//* Author       : Stefan Graf
//* Datum        : 12.08.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantStandartHints (const RefMand : Integer; const PickHint, PackHint : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_STANDARD_HINTS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pPickText',ftString,pdInput, 128, copy (PickHint, 1, 128));
    Parameters.CreateParameter('pPackText',ftString,pdInput, 128, copy (PackHint, 1, 128));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantReturnHint
//* Author       : Stefan Graf
//* Datum        : 09.04.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantReturnHint (const RefMand : Integer; const ReturnHint : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_RETURN_HINT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, RefMand);

    Parameters.CreateParameter('pReturnHint',ftString,pdInput, 512, copy (ReturnHint, 1, 512));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantArtikelConfig (const RefMand : Integer; const ArtikelSuffix : String; const ArtikelNext, ArtikelStart, ArtikelEnd, ArtikelLen : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_CONFIG_ARTIKEL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    Parameters.CreateParameter('pArtikelSuffix',ftString,pdInput, 32, copy (ArtikelSuffix, 1, 32));
    Parameters.CreateParameter('pArtikelNext',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelNext));
    Parameters.CreateParameter('pArtikelStart',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelStart));
    Parameters.CreateParameter('pArtikelEnd',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelEnd));
    Parameters.CreateParameter('pArtikelLen',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelLen));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantArtikelConfig (const RefMand : Integer; const ArtikelPrefix, ArtikelSuffix : String; const ArtikelNext, ArtikelStart, ArtikelEnd, ArtikelLen : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_CONFIG_ARTIKEL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    Parameters.CreateParameter('pArtikelPerfix',ftString,pdInput, 16, copy (ArtikelPrefix, 1, 16));
    Parameters.CreateParameter('pArtikelSuffix',ftString,pdInput, 32, copy (ArtikelSuffix, 1, 32));
    Parameters.CreateParameter('pArtikelNext',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelNext));
    Parameters.CreateParameter('pArtikelStart',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelStart));
    Parameters.CreateParameter('pArtikelEnd',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelEnd));
    Parameters.CreateParameter('pArtikelLen',ftInteger,pdInput, 12, GetPLSQLParameter (ArtikelLen));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantZollDaten
//* Author       : Stefan Graf
//* Datum        : 09.02.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantZollDaten (const RefMand : Integer; const OptAutoDeclaration : Char; const BaseTricNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_MANDANT_CONFIG_ZOLL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    Parameters.CreateParameter('pAutoDeclaration',ftString,pdInput, 1, OptAutoDeclaration);
    Parameters.CreateParameter('pTaricNr',ftString,pdInput, 32, copy (BaseTricNr, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetMandantPackTypeGroup
//* Author       : Stefan Graf
//* Datum        : 17.09.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetMandantPackTypeGroup (const RefMand : Integer; const PackGroup : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.SET_LT_PACKING_GROUP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    Parameters.CreateParameter('pPackGroup',ftString,pdInput, 32, copy (PackGroup, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetNextMandantArtikelNr (const RefMand : Integer; var ArtikelNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  ArtikelNr := '';

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.GET_NEXT_ARTIKEL_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefMand));

    Parameters.CreateParameter('oArNr',ftString,pdOutput, 32, NULL);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) then begin
    if not (StoredProcedure.Parameters.ParamValues ['oArNr'] = NULL) then
      ArtikelNr := StoredProcedure.Parameters.ParamValues ['oArNr'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateKommPlatz (const RefLager, RefLP : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_KOMM_VERWALTUNG.CREATE_KOMM_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefLP',ftInteger,pdInput, 12, RefLP);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 17.09.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateKommPlatz (const RefLager, RefLP : Integer; var RefKommLP : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  RefKommLP := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_KOMM_VERWALTUNG.CREATE_KOMM_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefLP',ftInteger,pdInput, 12, RefLP);

    Parameters.CreateParameter('oRefKommLP',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    RefKommLP := StoredProcedure.Parameters.ParamValues ['oRefKommLP'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteKommPlatz (const RefKommLP : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_KOMM_VERWALTUNG.DELETE_KOMM_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefKommLP',ftInteger,pdInput, 32, RefKommLP);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetKommPlatzEigenschaften (const RefKommLP : Integer; const FolgeNr, MaxArtikle : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_KOMM_VERWALTUNG.SET_KOMM_LP_PROPERTY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefKommLP',ftInteger,pdInput, 12, RefKommLP);

    if (FolgeNr = -1) then
      Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pKommFolge',ftInteger,pdInput, 12, FolgeNr);

    Parameters.CreateParameter('pMaxArtikel',ftInteger,pdInput, 12, MaxArtikle);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.10.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetKommNachschubPlatz (const RefKommLP, RefNachLP : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_KOMM_VERWALTUNG.SET_KOMM_LP_NACH_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefKommLP',ftInteger,pdInput, 12, RefKommLP);
    Parameters.CreateParameter('pRefNachLP',ftInteger,pdInput, 12, GetPLSQLParameter (RefNachLP));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetKommunikationsPartner (const Mandant, Lager, Vorgang, Partner : String; const ActiveFlag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SCHNITTSTELLE.DEF_IFC_PARTNER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pMandant',ftString,pdInput, 32, Mandant);
    Parameters.CreateParameter('pLager',ftString,pdInput, 32, Lager);
    Parameters.CreateParameter('pVorgang',ftString,pdInput, 32, Vorgang);
    Parameters.CreateParameter('pReceiver',ftString,pdInput, 32, Partner);

    if (ActiveFlag) then
      Parameters.CreateParameter('pActiveFlag',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pActiveFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateLocation  (const RefProject : Integer; const LocationName, Beschreibung, IFCID : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.CREATE_LOCATION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    if (RefProject = -1) then
      Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, NULL)
    else Parameters.CreateParameter('pRefProject',ftInteger,pdInput, 12, RefProject);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, IFCID);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 25.01.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CopyLocation (const OrigRef : Integer; const LocationName, Beschreibung, IFCID : String; var Ref : Integer) : Integer; overload;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.COPY_LOCATION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pOrigRef',ftInteger,pdInput, 12, OrigRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pIDIfc',ftString,pdInput, 32, IFCID);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  if (dbres = 0) Then
    LVSSecurityModule.UpdateSecurity;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLocation  (const LocationRef : Integer; const LocationName, Beschreibung, IFCID : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.CHANGE_LOCATION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pUserID',ftString,pdInput, 32, NULL);
    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LocationRef);
    Parameters.CreateParameter('pName',ftString,pdInput, 32, LocationName);
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 128, Beschreibung);
    Parameters.CreateParameter('pIDIfc',ftString,pdInput, 32, IFCID);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationAdresse (const LocationRef : Integer; const AdrText, RoadText, PLZText, OrtText, LandText: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_LOCATION_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pAdr',ftString,pdInput, 64, AdrText);
    Parameters.CreateParameter('pRoad',ftString,pdInput, 64, RoadText);
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 12, PLZText);
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, OrtText);
    Parameters.CreateParameter('pLand',ftString,pdInput, 8, LandText);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationInfos (const LocationRef : Integer; const Telefon, Fax, Contact, MailAdr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_LOCATION_INFOS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, Telefon);
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, Fax);
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, Contact);
    Parameters.CreateParameter('pMail',ftString,pdInput, 64, MailAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLocationLogin
//* Author       : Stefan Graf
//* Datum        : 11.01.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationLogin (const LocationRef, MaxSessions : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_LOCATION_LOGIN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pMaxSession',ftInteger,pdInput, 12, GetPLSQLParameter (MaxSessions));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLocationOptions
//* Author       : Stefan Graf
//* Datum        : 28.10.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationOptions (const LocationRef : Integer; const InVisCol, SetOpt, ConfigOpt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_LOCATION_OPTIONS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pInVisCol',ftString,pdInput, 256, SetOpt);
    Parameters.CreateParameter('pSetOpt',ftString,pdInput, 128, SetOpt);
    Parameters.CreateParameter('pCfgOpt',ftString,pdInput, 128, ConfigOpt);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLocationConfig
//* Author       : Stefan Graf
//* Datum        : 04.02..2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationConfig (const LocationRef : Integer; const ConfigSet : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_LOCATION_CONFIG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pConfig',ftString,pdInput, 256, ConfigSet);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 12.08.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLocationBioKontrollNr (const LocationRef : Integer; const KontrollNr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LOCATION.SET_BIO_KONTROLL_NR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftString,pdInput, 12, LocationRef);

    Parameters.CreateParameter('pKontrollNr',ftString,pdInput, 32, copy (KontrollNr, 1, 32));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.03.2016
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLPFolgeNr (const LPRef, FolgeNr : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.SET_REIHEN_FOLGE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);
    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, GetPLSQLParameter (FolgeNr));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLPTypenDaten (const LPRef : Integer; const Flag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_TYPEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);

    if (Flag) then
      Parameters.CreateParameter('pBesFlag',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pBesFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 01.08.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLPTypenOptions (const LPRef : Integer; const Options : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_TYPEN_OPTIONS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);
    Parameters.CreateParameter('pOptions',ftString, pdInput, 8, Options);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLPTypenDaten (const LPRef : Integer; const BesFlag, MixFlag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_TYPEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);

    if (BesFlag) then
      Parameters.CreateParameter('pBesFlag',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pBesFlag',ftString,pdInput, 1, '0');

    if (MixFlag) then
      Parameters.CreateParameter('pMixFlag',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pMixFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeLPBereich (const LPRef, LBRef, LBZoneRef : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LP_BEREICH';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);
    Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBRef);

    if (LBZoneRef = -1) then
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pLBRef',ftInteger,pdInput, 12, LBZoneRef);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLPLTZuordnung (const LPRef, LTRef : Integer; const Flag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_LPLT_ASSIGN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, LPRef);
    Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, LTRef);

    if (Flag) then
      Parameters.CreateParameter('pAllowedFlag',ftString,pdInput, 1, '1')
    else Parameters.CreateParameter('pAllowedFlag',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetLTMandantUse
//* Author       : Stefan Graf
//* Datum        : 23.02.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetLTMandantUse (const LTRef, RefMand : Integer; const Flag : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LT.CHANGE_USE_FOR_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLTRef',ftInteger,pdInput, 12, LTRef);
    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    if (Flag) then
      Parameters.CreateParameter('pFlag',ftString,pdInput, 1, '+')
    else Parameters.CreateParameter('pFlag',ftString,pdInput, 1, '-');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SperrenLagerplatz  (const Ref : Integer; const Grund : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.LP_SPERREN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, Ref);
    Parameters.CreateParameter('pGrund',ftString,pdInput, 64, copy (Grund, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FreigabeLagerplatz (const Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.LP_FREIGEBEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pLPRef',ftInteger,pdInput, 12, Ref);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteLagerplatz (const Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.DELETE_LP';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, Ref);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetFolgeNummern (const RefLB : Integer; const Reihe : string;
                          const PlatzVon,PlatzBis,TiefeVon,TiefeBis,EbeneVon,EbeneBis : Integer;
                          const Art : String;
                          const Start, Step : Integer;
                          const ChangeLPNr : Boolean) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.SET_REIHEN_FOLGE';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefLB',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pReihe',ftString,pdInput, 5, Reihe);
    Parameters.CreateParameter('pPlatzVon',ftInteger,pdInput, 12, PlatzVon);
    Parameters.CreateParameter('pPlatzBis',ftInteger,pdInput, 12, PlatzBis);

    if (LVSDatenModul.DatabaseVersion > 41) then begin
      Parameters.CreateParameter('pVonTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeVon));
      Parameters.CreateParameter('pBisTiefe',ftInteger,pdInput, 12, GetPLSQLParameter (TiefeBis));
    end;
    
    Parameters.CreateParameter('pEbeneVon',ftInteger,pdInput, 12, EbeneVon);
    Parameters.CreateParameter('pEbeneBis',ftInteger,pdInput, 12, EbeneBis);
    Parameters.CreateParameter('pArt',ftString,pdInput, 8, Art);
    Parameters.CreateParameter('pStart',ftInteger,pdInput, 12, Start);
    Parameters.CreateParameter('pStep',ftInteger,pdInput, 12, Step);

    if (ChangeLPNr) then
      Parameters.CreateParameter('pChangeLPNr',ftString,pdInput, 1, '1')
    else
      Parameters.CreateParameter('pChangeLPNr',ftString,pdInput, 1, '0');

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

function ChangeRegal (const RefRegal, RefGasse : Integer; const Bezeichnung : String; const FachLast, EbeneLast, FeldLast : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_LP.CHANGE_REGAL';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefRegal',ftInteger,pdInput, 12, RefRegal);
    Parameters.CreateParameter('pRefGasse',ftInteger,pdInput, 12, GetPLSQLParameter (RefGasse));
    Parameters.CreateParameter('pBezeichnung',ftString,pdInput, 32, Bezeichnung);
    Parameters.CreateParameter('pFachLast',ftInteger,pdInput, 12, GetPLSQLParameter (FachLast));
    Parameters.CreateParameter('pEbeneLast',ftInteger,pdInput, 12, GetPLSQLParameter (EbeneLast));
    Parameters.CreateParameter('pFeldLast',ftInteger,pdInput, 12, GetPLSQLParameter (FeldLast));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


function Set_LB_INV(const RefLB: Integer; const Status: String; const IntervalDays: Integer; const MaxLpCount: Integer; const MinLpAge: Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do
  begin
    ProcedureName := 'PA_LB.SET_LB_INV';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pRefLB',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pStatus',ftString,pdInput, 32, Status);
    Parameters.CreateParameter('pIntervalDays',ftInteger,pdInput, 32, IntervalDays);
    Parameters.CreateParameter('pMaxLpCount',ftInteger,pdInput, 32, MaxLpCount);
    Parameters.CreateParameter('pMinLpAge',ftInteger,pdInput, 32, MinLpAge);
    StoredProcedure.Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    StoredProcedure.Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
  StoredProcedure.Free;
  Result := dbres;
end;

function Set_LB_RUECK(const RefLB: Integer; const RefMHDLB: Integer; const Status: String; const Fuellgrad: Integer; const FreeLpCount: Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;
  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do
  begin
    ProcedureName := 'PA_LB.SET_LB_RUECK';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);
    Parameters.CreateParameter('pRefLB',ftInteger,pdInput, 12, RefLB);
    Parameters.CreateParameter('pRefMHDLB',ftInteger,pdInput, 12, GetPLSQLParameter(RefMHDLB));
    Parameters.CreateParameter('pStatus',ftString,pdInput, 32, Status);
    Parameters.CreateParameter('pFuellgrad',ftInteger,pdInput, 32, Fuellgrad);
    Parameters.CreateParameter('pFreeLpCount',ftInteger,pdInput, 32, FreeLpCount);
    StoredProcedure.Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    StoredProcedure.Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);
  StoredProcedure.Free;
  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Sebastian Sch�tte
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function InsertMandantBesMandant     (const RefMand : Integer; const RefBesMan : Integer; const RefLager : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.INSERT_MANDANT_BES_MANDANT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefBesMand',ftInteger,pdInput, 12, RefBesMan);
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Sebastian Sch�tte
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ClearMandantBesMandant   (const RefMand : Integer; const RefLager : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_MANDANT.CLEAR_MANDANT_BES_MANDANTEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

end.

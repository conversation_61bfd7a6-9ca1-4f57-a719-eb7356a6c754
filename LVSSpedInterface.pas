unit LVSSpedInterface;

interface

function CreateSpedition     (const RefLoc, RefMand, RefLager : Integer; const SpedNr, SpedName, SpedKennung, SpedDesc, MailAddrAvis : String; const RefRelation, RefPackRelation, RefWELeerKonto, RefWALeerKonto : Integer; const LabelArt, ChangeOpt, Options : String; var Ref : Integer) : Integer;
function ChangeSpedition     (const RefSped, UpdKey, RefLager : Integer; const SpedNr, SpedName, SpedKennung, SpedDesc, MailAddrAvis : String; const RefRelation, RefPackRelation, RefWELeerKonto, RefWALeerKonto : Integer; const LabelArt, ChangeOpt, Options : String) : Integer;
function DeleteSpedition     (const RefSped : Integer) : Integer;
function SetSpeditionAdresse (const RefSped : Integer; const Adr<PERSON><PERSON>, AdrName1, <PERSON>r<PERSON>ame2, Adr<PERSON>ex<PERSON>, Road1Text, Road2Text, PLZText, Ort<PERSON>ex<PERSON>, LandText, Telefon, Fax, Contact, MailAdr: String) : Integer;
function SetIFTMINDaten      (const RefSped : Integer; const Kennung, ILN, AccountNr, Depot, DFUEArt : String) : Integer;

function ChangeSpeditionConfig      (const RefSped : Integer; const RefDefaultLT : Integer; const LogPlatform, OptStr : String) : Integer;
function SetSpeditionCutOffTime     (const RefSped : Integer; const CutOffTime : Integer) : Integer;
function SetSpeditionAnlieferDepot  (const RefSped : Integer; const RefDepot : Integer; const OptStr : String) : Integer;
function SetSpeditionVersandart     (const RefSped : Integer; const VersandArt : String) : Integer;

function CreateSpeditionAvis    (const RefMand, RefSped : Integer; const VerladeDatum : TDateTime; const Options : String; var RefAvis : Integer) : Integer;
function ChangeSpeditionAvisPos (const RefAvisPos, RefLT, Anzahl, NettoGewicht, BruttoGewicht : Integer) : Integer;
function DeleteSpeditionAvisPos (const RefAvisPos : Integer) : Integer;
function SendSpeditionAvis      (const RefAvis : Integer) : Integer;

function CreateSpedGateway        (const RefLager, RefSped, RefMand, RefSubMand, RefTrader : Integer; const Countries, Status : String; var RefGateway : Integer) : Integer;
function DeleteSpedGateway        (const RefGateway : Integer) : Integer;
function SetSpedGatewayStatus     (const RefGateway : Integer; const Status : String) : Integer;
function SetSpedGatewayAssigend   (const RefGateway : Integer; const RefLager, RefMand, RefSubMand, RefTrader : Integer; const Countries : String) : Integer;
function SetSpedGatewayParameter  (const RefGateway : Integer; const Setting, IncoTerm, Options : String) : Integer;
function SetSpedGatewayAbsender   (const RefGateway : Integer; const Absender : String) : Integer;
function SetSpedGatewayProdukt    (const RefGateway : Integer; const Gateway, Location, Client, ProductNat, ProductInt, ProductRet : String) : Integer;

implementation

uses DB, ADODB, Variants, DatenModul;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateSpedition (const RefLoc, RefMand, RefLager : Integer; const SpedNr, SpedName, SpedKennung, SpedDesc, MailAddrAvis : String; const RefRelation, RefPackRelation, RefWELeerKonto, RefWALeerKonto : Integer; const LabelArt, ChangeOpt, Options : String; var Ref : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    Connection    := LVSDatenModul.MainADOConnection;
    ProcedureName := 'PA_SPEDITION.CREATE_SPEDITION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pSender',ftString,pdInput, 32, 'PCD');

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, RefMand);

    if (RefLoc = -1) then
      Parameters.CreateParameter('pRefLocation',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefLocation',ftInteger,pdInput, 12, RefLoc);

    if (RefLager = -1) then
      Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);

    Parameters.CreateParameter('pName',ftString,pdInput, 32, SpedName);
    Parameters.CreateParameter('pKennung',ftString,pdInput, 32, SpedKennung);
    Parameters.CreateParameter('pBeschreibung',ftString,pdInput, 64, SpedDesc);
    Parameters.CreateParameter('pNummer',ftString,pdInput, 16, SpedNr);

    Parameters.CreateParameter('pRefRelation',ftInteger,pdInput, 12, GetPLSQLParameter (RefRelation));
    Parameters.CreateParameter('pRefPackRelation',ftInteger,pdInput, 12, GetPLSQLParameter (RefPackRelation));

    Parameters.CreateParameter('pRefWELeerKonto',ftInteger,pdInput, 12, GetPLSQLParameter (RefWELeerKonto));
    Parameters.CreateParameter('pRefWALeerKonto',ftInteger,pdInput, 12, GetPLSQLParameter (RefWALeerKonto));

    Parameters.CreateParameter('pLabelArt',ftString,pdInput, 32, LabelArt);

    Parameters.CreateParameter('pChangeOpt',ftString,pdInput, 32, ChangeOpt);

    if (LVSDatenModul.DatabaseVersion > 36) then begin
      Parameters.CreateParameter('pOptions',ftString,pdInput, 32, Options);
    end;

    Parameters.CreateParameter('pMailAddrAvis',ftString,pdInput, 128, MailAddrAvis);

    Parameters.CreateParameter('oRef',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then
    Ref := StoredProcedure.Parameters.ParamValues ['oRef'];

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeSpedition (const RefSped, UpdKey, RefLager : Integer; const SpedNr, SpedName, SpedKennung, SpedDesc, MailAddrAvis : String; const RefRelation, RefPackRelation, RefWELeerKonto, RefWALeerKonto : Integer; const LabelArt, ChangeOpt, Options : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    Connection    := LVSDatenModul.MainADOConnection;
    ProcedureName := 'PA_SPEDITION.CHANGE_SPEDITION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefSped);

    if (UpdKey = -1) then
      Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pUpdKey',ftInteger,pdInput, 12, UpdKey);

    if (RefLager = -1) then
      Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, NULL)
    else
      Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);

    Parameters.CreateParameter('pName',ftString,pdInput, 32, SpedName);
    Parameters.CreateParameter('pKennung',ftString,pdInput, 32, SpedKennung);
    Parameters.CreateParameter('pBeschreibung',ftString,pdInput, 64, SpedDesc);
    Parameters.CreateParameter('pNummer',ftString,pdInput, 16, SpedNr);

    Parameters.CreateParameter('pRefRelation',ftInteger,pdInput, 12, GetPLSQLParameter (RefRelation));
    Parameters.CreateParameter('pRefPackRelation',ftInteger,pdInput, 12, GetPLSQLParameter (RefPackRelation));

    Parameters.CreateParameter('pRefWELeerKonto',ftInteger,pdInput, 12, GetPLSQLParameter (RefWELeerKonto));
    Parameters.CreateParameter('pRefWALeerKonto',ftInteger,pdInput, 12, GetPLSQLParameter (RefWALeerKonto));

    Parameters.CreateParameter('pLabelArt',ftString,pdInput, 32, LabelArt);

    Parameters.CreateParameter('ChangeOpt',ftString,pdInput, 32, ChangeOpt);

    if (LVSDatenModul.DatabaseVersion > 36) then begin
      Parameters.CreateParameter('pOptions',ftString,pdInput, 32, Options);
    end;

    Parameters.CreateParameter('pMailAddrAvis',ftString,pdInput, 128, MailAddrAvis);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteSpedition (const RefSped : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    Connection    := LVSDatenModul.MainADOConnection;
    ProcedureName := 'PA_SPEDITION.DELETE_SPEDITION';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRef',ftInteger,pdInput, 12, RefSped);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpeditionAdresse (const RefSped : Integer; const AdrILN, AdrName1, AdrName2, AdrText, Road1Text, Road2Text, PLZText, OrtText, LandText, Telefon, Fax, Contact, MailAdr: String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPEDITION_ADR';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);

    Parameters.CreateParameter('pILN',ftString,pdInput, 64, AdrILN);
    Parameters.CreateParameter('pName1',ftString,pdInput, 64, AdrName1);
    Parameters.CreateParameter('pName2',ftString,pdInput, 64, AdrName2);
    Parameters.CreateParameter('pNameZusatz',ftString,pdInput, 64, AdrText);
    Parameters.CreateParameter('pStrasse1',ftString,pdInput, 64, Road1Text);
    Parameters.CreateParameter('pStrasse2',ftString,pdInput, 64, Road2Text);
    Parameters.CreateParameter('pPLZ',ftString,pdInput, 12, copy (PLZText, 1, 12));
    Parameters.CreateParameter('pOrt',ftString,pdInput, 64, OrtText);
    Parameters.CreateParameter('pLand',ftString,pdInput, 16, copy (LandText, 1, 16));
    Parameters.CreateParameter('pContact',ftString,pdInput, 64, Contact);
    Parameters.CreateParameter('pTelefon',ftString,pdInput, 32, copy (Telefon, 1, 32));
    Parameters.CreateParameter('pFax',ftString,pdInput, 32, copy (Fax, 1, 32));
    Parameters.CreateParameter('pMail',ftString,pdInput, 64, MailAdr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetIFTMINDaten (const RefSped : Integer; const Kennung, ILN, AccountNr, Depot, DFUEArt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_IFTMIN_DATEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);

    Parameters.CreateParameter('pKennung',ftString,pdInput, 16, Kennung);
    Parameters.CreateParameter('pILN',ftString,pdInput, 16, ILN);
    Parameters.CreateParameter('pAccountNr',ftString,pdInput, 32, AccountNr);
    Parameters.CreateParameter('pDepot',ftString,pdInput, 32, Depot);
    Parameters.CreateParameter('pDFUEArt',ftString,pdInput, 32, DFUEArt);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: ChangeSpeditionConfig
//* Author       : Stefan Graf
//* Datum        : 25.01.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeSpeditionConfig (const RefSped : Integer; const RefDefaultLT : Integer; const LogPlatform, OptStr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.CHANGE_SPEDITION_CONFIG';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pRefDefaultLT',ftInteger,pdInput, 12, GetPLSQLParameter (RefDefaultLT));
    Parameters.CreateParameter('pLogPlatform',ftString,pdInput, 32, LogPlatform);

    Parameters.CreateParameter('pOptions',ftString,pdInput, 256, OptStr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpeditionAnlieferDepot
//* Author       : Stefan Graf
//* Datum        : 18.07.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpeditionAnlieferDepot (const RefSped : Integer; const RefDepot : Integer; const OptStr : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPEDITION_ANLIEFER_DEPOT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pRefDepot',ftInteger,pdInput, 12, GetPLSQLParameter (RefDepot));
    Parameters.CreateParameter('pDepotPflicht',ftString,pdInput, 1, OptStr);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpeditionVersandart
//* Author       : Stefan Graf
//* Datum        : 18.07.2019
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpeditionVersandart (const RefSped : Integer; const VersandArt : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPEDITION_VERSAND_ART';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pVersandArt',ftString,pdInput, 64, copy (VersandArt, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpeditionCutOffTime
//* Author       : Stefan Graf
//* Datum        : 18.08.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpeditionCutOffTime (const RefSped : Integer; const CutOffTime : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPEDITION_CUT_OFF';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pCutOffTime',ftInteger,pdInput, 12, GetPLSQLParameter (CutOffTime));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: CreateSpeditionAvis
//* Author       : Stefan Graf
//* Datum        : 29.11.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateSpeditionAvis (const RefMand, RefSped : Integer; const VerladeDatum : TDateTime; const Options : String; var RefAvis : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  RefAvis := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_IFTMIN.CREATE_SPED_AVIS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefMand));
    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pVerladeDatum',ftDate,pdInput, 12, GetPLSQLParameter (VerladeDatum));
    Parameters.CreateParameter('pOptions',ftString,pdInput, 32, Options);

    Parameters.CreateParameter('oRefAvis',ftInteger,pdOutput, 12, 0);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 1024, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then begin
    if not VarIsNull (StoredProcedure.Parameters.FindParam ('oRefAvis').Value) then
      RefAvis := StoredProcedure.Parameters.ParamValues ['oRefAvis'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SendSpeditionAvis
//* Author       : Stefan Graf
//* Datum        : 29.11.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SendSpeditionAvis (const RefAvis : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_IFTMIN.SEND_AVIS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvis',ftInteger,pdInput, 12, RefAvis);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: ChangeSpeditionAvisPos
//* Author       : Stefan Graf
//* Datum        : 04.12.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ChangeSpeditionAvisPos (const RefAvisPos, RefLT, Anzahl, NettoGewicht, BruttoGewicht : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_IFTMIN.CHANGE_AVIS_POS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, RefAvisPos);
    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, RefLT);
    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, Anzahl);
    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, GetPLSQLParameter(NettoGewicht));
    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, GetPLSQLParameter(BruttoGewicht));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DeleteSpeditionAvisPos
//* Author       : Stefan Graf
//* Datum        : 29.11.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteSpeditionAvisPos (const RefAvisPos : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_IFTMIN.DELETE_AVIS_POS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefAvisPos',ftInteger,pdInput, 12, RefAvisPos);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


//******************************************************************************
//* Function Name: SetSpedGatewayStatus
//* Author       : Stefan Graf
//* Datum        : 24.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CreateSpedGateway (const RefLager, RefSped, RefMand, RefSubMand, RefTrader : Integer; const Countries, Status : String; var RefGateway : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  RefGateway := -1;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.CREATE_SPED_GATEWAY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefLocation',ftInteger,pdInput, 12, LVSDatenModul.AktLocationRef);
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefSped',ftInteger,pdInput, 12, RefSped);
    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefMand));
    Parameters.CreateParameter('pRefSubMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefSubMand));
    Parameters.CreateParameter('pRefTrader',ftInteger,pdInput, 12, GetPLSQLParameter (RefTrader));
    Parameters.CreateParameter('pCountries',ftString,pdInput, 64, copy (Countries, 1, 64));
    Parameters.CreateParameter('pStatus',ftString,pdInput, 5, copy (Status, 1, 5));

    Parameters.CreateParameter('oRefGateway',ftInteger,pdOutput, 12, RefGateway);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  if (dbres = 0) Then begin
    if not VarIsNull (StoredProcedure.Parameters.FindParam ('oRefGateway').Value) then
      RefGateway := StoredProcedure.Parameters.ParamValues ['oRefGateway'];
  end;

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: DeleteSpedGateway
//* Author       : Stefan Graf
//* Datum        : 27.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteSpedGateway (const RefGateway : Integer) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.DELETE_SPED_GATEWAY';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpedGatewayStatus
//* Author       : Stefan Graf
//* Datum        : 24.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpedGatewayStatus (const RefGateway : Integer; const Status : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPED_GATEWAY_STATUS';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);
    Parameters.CreateParameter('pStatus',ftString,pdInput, 5, copy (Status, 1, 5));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpedGatewayStatus
//* Author       : Stefan Graf
//* Datum        : 24.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpedGatewayAssigend (const RefGateway : Integer; const RefLager, RefMand, RefSubMand, RefTrader : Integer; const Countries : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPED_GATEWAY_ASSIGNED';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);
    Parameters.CreateParameter('pRefLager',ftInteger,pdInput, 12, RefLager);
    Parameters.CreateParameter('pRefMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefMand));
    Parameters.CreateParameter('pRefSubMand',ftInteger,pdInput, 12, GetPLSQLParameter (RefSubMand));
    Parameters.CreateParameter('pRefTrader',ftInteger,pdInput, 12, GetPLSQLParameter (RefTrader));
    Parameters.CreateParameter('pCountries',ftString,pdInput, 64, copy (Countries, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpedGatewayParameter
//* Author       : Stefan Graf
//* Datum        : 23.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpedGatewayParameter (const RefGateway : Integer; const Setting, IncoTerm, Options : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPED_GATEWAY_DATEN';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);
    Parameters.CreateParameter('pSetting',ftString,pdInput, 64, copy (Setting, 1, 64));
    Parameters.CreateParameter('pIncoTerm',ftString,pdInput, 64, copy (IncoTerm, 1, 64));
    Parameters.CreateParameter('pOptions',ftString,pdInput, 64, copy (Options, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpedGatewayAbsender
//* Author       : Stefan Graf
//* Datum        : 01.06.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpedGatewayAbsender (const RefGateway : Integer; const Absender : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPED_GATEWAY_ABSENDER';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);
    Parameters.CreateParameter('pAbsender',ftString,pdInput, 64, copy (Absender, 1, 64));

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;

//******************************************************************************
//* Function Name: SetSpedGatewayProdukt
//* Author       : Stefan Graf
//* Datum        : 23.02.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function SetSpedGatewayProdukt (const RefGateway : Integer; const Gateway, Location, Client, ProductNat, ProductInt, ProductRet : String) : Integer;
var
  dbres : Integer;
  StoredProcedure : TADOStoredProc;
begin
  dbres := 0;

  StoredProcedure := TADOStoredProc.Create (Nil);

  with StoredProcedure do begin
    ProcedureName := 'PA_SPEDITION.SET_SPED_GATEWAY_PRODUKT';

    Parameters.CreateParameter('Result',ftInteger,pdReturnValue, 12, 0);

    Parameters.CreateParameter('pRefGateway',ftInteger,pdInput, 12, RefGateway);
    Parameters.CreateParameter('pGateway',ftString,pdInput, 64, copy (Gateway, 1, 64));
    Parameters.CreateParameter('pLocation',ftString,pdInput, 64, copy (Location, 1, 64));
    Parameters.CreateParameter('pClient',ftString,pdInput, 64, copy (Client, 1, 64));
    Parameters.CreateParameter('pProductNat',ftString,pdInput, 64, copy (ProductNat, 1, 64));
    Parameters.CreateParameter('pProductInt', ftString,pdInput, 64, copy (ProductInt, 1, 64));

    if (Length (ProductRet) > 0) then begin
      Parameters.CreateParameter('pProductRet', ftString,pdInput, 64, copy (ProductRet, 1, 64));
    end;

    Parameters.CreateParameter('pErrorCode',ftInteger,pdOutput, 12, 0);
    Parameters.CreateParameter('pErrorText',ftString,pdOutput, 256, '');
  end;

  dbres := LVSDatenModul.CallStoreProcedure (StoredProcedure);

  StoredProcedure.Free;

  Result := dbres;
end;


end.

unit VASInputDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls;

type
  TVASInputForm = class(TForm)
    VASScrollBox: TScrollBox;
    TopPanel: TPanel;
    BottomPanel: TPanel;
    AbortButton: TButton;
    OkButton: TButton;
    Bevel1: TBevel;
    Label1: TLabel;
    Label2: TLabel;
    RetoureLabel: TLabel;
    ArtikelLabel: TLabel;
    Bevel2: TBevel;
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    fRefMand,
    fRefSubMand,
    fRefLager     : Integer;
    fRefWE,
    fRefWEPos     : Integer;
    fInputList    : TList;
  public
    function PrepareReturn (const RefRet : Integer; const ProcStep : String; var EntryCount : Integer) : Integer;
    function PrepareReturnPos (const RefRetPos : Integer; const ProcStep : String; var EntryCount : Integer) : Integer;
  end;

implementation

{$R *.dfm}

uses
  Ora, OraSmart, DatenModul, DBGridUtilModule, ConfigModul, LVSDatenInterface, FrontendUtils,
  SprachModul, VASInputFRM;

procedure TVASInputForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res,
  ref,
  idx,
  reftmepl : Integer;
  stat     : String;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    idx := 0;
    res := 0;

    while (idx < fInputList.Count) and (res = 0) do begin
      reftmepl := TVASInputFrame (fInputList [idx]).RefTempl;

      if (TVASInputFrame (fInputList [idx]).VASCheckBox.State = cbUnchecked) then
        stat := '0'
      else if (TVASInputFrame (fInputList [idx]).VASCheckBox.State = cbChecked) then
        stat := '1'
      else
        stat := '';

      res := InsertWEWorkload (fRefWE, fRefWEPos, reftmepl, -1, -1, stat, '', '', '', '', ref);

      Inc (idx);
    end;

    CanClose := (res = 0);
  end;
end;

procedure TVASInputForm.FormCreate(Sender: TObject);
begin
  fInputList := TList.Create;
end;

procedure TVASInputForm.FormShow(Sender: TObject);
begin
  Label2.Visible := ArtikelLabel.Visible;
end;

function TVASInputForm.PrepareReturnPos (const RefRetPos : Integer; const ProcStep : String; var EntryCount : Integer) : Integer;
var
  textstr   : String;
  inpframe  : TVASInputFrame;
  query     : TSmartQuery;
  selquery  : TSmartQuery;
begin
  fRefWEPos := RefRetPos;
  
  EntryCount := 0;

  query := TSmartQuery.Create (Self);
  selquery := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;

    selquery.SQL.Add ('select * from V_WE_WORKLOAD where REF_WE_POS=:ref_we_pos and REF_WORKLOAD_TEMPLATE=:ref_templ');
    selquery.Params.ParamByName ('ref_we_pos').Value := RefRetPos;

    query.SQL.Add ('select we.REF as REF_WE,we.REF_MAND,we.REF_SUB_MAND,we.REF_LAGER,we.EINGANGS_NR,wrp.ARTIKEL_NR,wrp.ARTIKEL_TEXT from V_WE_RET_POS wrp, VQ_WARENEINGANG we where we.REF=wrp.REF_WE and wrp.REF=:ref');
    query.Params.ParamByName ('ref').Value := RefRetPos;

    try
      query.Open;

      fRefWE      := query.FieldbyName ('REF_WE').AsInteger;
      fRefMand    := query.FieldbyName ('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldbyName ('REF_SUB_MAND'));
      fRefLager   := query.FieldbyName ('REF_LAGER').AsInteger;

      RetoureLabel.Caption := query.FieldbyName ('EINGANGS_NR').AsString;
      ArtikelLabel.Caption := query.FieldbyName ('ARTIKEL_NR').AsString + ' / ' + query.FieldbyName ('ARTIKEL_TEXT').AsString;

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where instr (PROCESS_STEP, :step) > 0 and STATUS=''AKT''');
      query.Params.ParamByName ('step').Value := ProcStep;

      if (fRefSubMand > 0) then begin
        query.SQL.Add ('and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
        query.Params.ParamByName ('ref_sub_mand').Value := fRefSubMand;
      end else begin
        query.SQL.Add ('and REF_SUB_MAND is null and (REF_MAND is null or REF_MAND=:ref_mand)');
      end;
      query.Params.ParamByName ('ref_mand').Value := fRefMand;

      query.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager) and (PROCESS_TYPE is null or PROCESS_TYPE=:art) order by SEQUENCE asc nulls last');
      query.Params.ParamByName ('ref_lager').Value := fRefLager;
      query.Params.ParamByName ('art').Value := 'RETURN';

      query.Open;

      while not query.Eof do begin
        inpframe := TVASInputFrame.Create (Self);
        inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
        inpframe.Parent := VASScrollBox;

        if not (query.FieldbyName ('INFO_TEXT').IsNull) then
          textstr := query.FieldbyName ('INFO_TEXT').AsString
        else
          textstr := query.FieldbyName ('NAME').AsString;

        inpframe.VASCheckBox.Caption := textstr;
        inpframe.RefTempl := query.FieldbyName ('REF').AsInteger;

        fInputList.Add (inpframe);

        selquery.Params.ParamByName ('ref_templ').Value := query.FieldbyName ('REF').AsInteger;

        selquery.Open;

        if selquery.FieldByName ('CHECK_RESULT').IsNull then
          inpframe.VASCheckBox.State := cbGrayed
        else
          inpframe.VASCheckBox.Checked := selquery.FieldByName ('CHECK_RESULT').AsString = '1';

        selquery.Close;

        Inc (EntryCount);

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  (*
  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;
  Inc (EntryCount);

  inpframe.VASCheckBox.Caption := 'Test 1';

  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;
  Inc (EntryCount);

  inpframe.VASCheckBox.Caption := 'Test 3';

  inpframe := TVASInputFrame.Create (Self);
  inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
  inpframe.Parent := VASScrollBox;

  inpframe.VASCheckBox.Caption := 'Test 3';
  Inc (EntryCount);
  *)

  Result := 0;
end;

function TVASInputForm.PrepareReturn (const RefRet : Integer; const ProcStep : String; var EntryCount : Integer) : Integer;
var
  inpframe  : TVASInputFrame;
  query     : TSmartQuery;
  selquery  : TSmartQuery;
begin
  fRefWE := RefRet;

  EntryCount := 0;

  ArtikelLabel.Visible := false;

  query := TSmartQuery.Create (Self);
  selquery := TSmartQuery.Create (Self);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    selquery.ReadOnly := True;
    selquery.Session := LVSDatenModul.OraMainSession;

    selquery.SQL.Add ('select * from V_WE_WORKLOAD where REF_WE_POS is null and REF_WE=:ref_we and REF_WORKLOAD_TEMPLATE=:ref_templ');
    selquery.Params.ParamByName ('ref_we').Value := RefRet;

    query.SQL.Add ('select we.REF as REF_WE,we.REF_MAND,we.REF_SUB_MAND,we.REF_LAGER,we.EINGANGS_NR from VQ_WARENEINGANG we where we.REF=:ref');
    query.Params.ParamByName ('ref').Value := RefRet;

    try
      query.Open;

      fRefWE      := query.FieldbyName ('REF_WE').AsInteger;
      fRefMand    := query.FieldbyName ('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldbyName ('REF_SUB_MAND'));
      fRefLager   := query.FieldbyName ('REF_LAGER').AsInteger;

      RetoureLabel.Caption := query.FieldbyName ('EINGANGS_NR').AsString;

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_PCD_WORKLOAD_TEMPLATE where instr (PROCESS_STEP, :step) > 0 and STATUS=''AKT''');
      query.Params.ParamByName ('step').Value := ProcStep;

      if (fRefSubMand > 0) then begin
        query.SQL.Add ('and (REF_MAND is null or REF_MAND=:ref_mand) and (REF_SUB_MAND is null or REF_SUB_MAND=:ref_sub_mand)');
        query.Params.ParamByName ('ref_sub_mand').Value := fRefSubMand;
      end else begin
        query.SQL.Add ('and REF_SUB_MAND is null and (REF_MAND is null or REF_MAND=:ref_mand)');
      end;
      query.Params.ParamByName ('ref_mand').Value := fRefMand;

      query.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager) and (PROCESS_TYPE is null or PROCESS_TYPE=:art) order by SEQUENCE asc nulls last');
      query.Params.ParamByName ('ref_lager').Value := fRefLager;
      query.Params.ParamByName ('art').Value := 'RETURN';

      query.Open;

      while not query.Eof do begin
        inpframe := TVASInputFrame.Create (Self);
        inpframe.Name := 'VASInputFrame_'+IntToStr (ComponentCount);
        inpframe.Parent := VASScrollBox;
        inpframe.VASCheckBox.Caption := query.FieldbyName ('NAME').AsString;
        inpframe.RefTempl := query.FieldbyName ('REF').AsInteger;

        fInputList.Add (inpframe);

        selquery.Params.ParamByName ('ref_templ').Value := query.FieldbyName ('REF').AsInteger;

        selquery.Open;

        if selquery.FieldByName ('CHECK_RESULT').IsNull then
          inpframe.VASCheckBox.State := cbGrayed
        else
          inpframe.VASCheckBox.Checked := selquery.FieldByName ('CHECK_RESULT').AsString = '1';

        selquery.Close;

        Inc (EntryCount);

        query.Next;
      end;

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

end.

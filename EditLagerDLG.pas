unit EditLagerDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ComboBoxPro, ComCtrls;

type
  TEditLagerForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    Bevel1: TBevel;
    LagerPanel: TPanel;
    Label4: TLabel;
    LocComboBox: TComboBoxPro;
    MandantPanel: TPanel;
    Label11: TLabel;
    MandComboBox: TComboBoxPro;
    Bevel2: TBevel;
    DatenPanel: TPanel;
    Label1: TLabel;
    Label2: TLabel;
    Label3: TLabel;
    Label9: TLabel;
    Label10: TLabel;
    Bevel5: TBevel;
    Label12: TLabel;
    Label13: TLabel;
    NameEdit: TEdit;
    BeschreibungEdit: TEdit;
    BetriebEdit: TEdit;
    ILNEdit: TEdit;
    EUNrEdit: TEdit;
    PageControl1: TPageControl;
    WETabSheet: TTabSheet;
    OptWEImCheckBox: TCheckBox;
    KommTabSheet: TTabSheet;
    WATabSheet: TTabSheet;
    OptCloseLFCheckBox: TCheckBox;
    OptVerkaufCheckBox: TCheckBox;
    LiefRetCheckBox: TCheckBox;
    ArtComboBox: TComboBoxPro;
    SperrLagerComboBox: TComboBoxPro;
    HKLComboBox: TComboBoxPro;
    Label16: TLabel;
    QSTabSheet: TTabSheet;
    QSWEComboBox: TComboBoxPro;
    Label19: TLabel;
    Label20: TLabel;
    QSRETComboBox: TComboBoxPro;
    AdressPanel: TPanel;
    Label5: TLabel;
    AdrEdit: TEdit;
    Label6: TLabel;
    RoadEdit: TEdit;
    Label7: TLabel;
    PLZEdit: TEdit;
    Label14: TLabel;
    FonEdit: TEdit;
    Bevel3: TBevel;
    MailEdit: TEdit;
    Label18: TLabel;
    FaxEdit: TEdit;
    Label15: TLabel;
    Label8: TLabel;
    OrtEdit: TEdit;
    Label21: TLabel;
    LandEdit: TEdit;
    Label17: TLabel;
    ContactEdit: TEdit;
    OptWEPosAKTCheckBox: TCheckBox;
    AutoCloseBestellungCheckBox: TCheckBox;
    ErsatzpickCheckBox: TCheckBox;
    FehlwareCheckInvCheckBox: TCheckBox;
    NullInvCheckBox: TCheckBox;
    FehlwareInvCheckBox: TCheckBox;
    AufPlanTabSheet: TTabSheet;
    PlanVollPALCheckBox: TCheckBox;
    PlanNVEPalFaktorCheckBox: TCheckBox;
    KommOptCheckBox: TCheckBox;
    SumResCheckBox: TCheckBox;
    RetoureTabSheet: TTabSheet;
    AutoCloseRetourenAvisCheckBox: TCheckBox;
    OptRETImCheckBox: TCheckBox;
    OPTRETPosAKTCheckBox: TCheckBox;
    BestandTabSheet: TTabSheet;
    BesMergeCheckBox: TCheckBox;
    OptWEBesAKTBestCloseCheckBox: TCheckBox;
    Label22: TLabel;
    IFCIDEdit: TEdit;
    NoteDateReqCheckBox: TCheckBox;
    NoteDateDefaultCheckBox: TCheckBox;
    DeliveryDateReqCheckBox: TCheckBox;
    DeliveryDateDefaultCheckBox: TCheckBox;
    MaxInvMengeEdit: TEdit;
    Label23: TLabel;
    Label24: TLabel;
    BestandCheckBox: TCheckBox;
    Label25: TLabel;
    NaheNullEdit: TEdit;
    Label26: TLabel;
    BesCheckKommBeginCheckBox: TCheckBox;
    AutoCheckInvCheckBox: TCheckBox;
    RetUniqueIDCheckBox: TCheckBox;
    RetNotAKTCheckBox: TCheckBox;
    PlanTourPalCheckBox: TCheckBox;
    AutoInvArCheckBox: TCheckBox;
    NoteNoDutyCheckBox: TCheckBox;
    procedure NumEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure ArtComboBoxChange(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure FehlwareInvCheckBoxClick(Sender: TObject);
    procedure OptWEBesAKTBestCloseCheckBoxClick(Sender: TObject);
    procedure AutoCheckInvCheckBoxClick(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, SprachModul, ResourceText;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.FormShow(Sender: TObject);
begin
  if not(LagerPanel.Visible) then
    Height := Height - LagerPanel.Height;

  if not(MandantPanel.Visible) then
    Height := Height - MandantPanel.Height;

  Label22.Visible := IFCIDEdit.Visible;

  Label23.Visible := MaxInvMengeEdit.Visible;
  Label24.Visible := MaxInvMengeEdit.Visible;

  Label25.Visible := NaheNullEdit.Visible;
  Label26.Visible := NaheNullEdit.Visible;

  ArtComboBoxChange(Sender);
  FehlwareInvCheckBoxClick(Sender);
  AutoCheckInvCheckBoxClick(Sender);
  OptWEBesAKTBestCloseCheckBoxClick(Sender);
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.NumEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not(Key in [#8, ^C, ^V, '0' .. '9']) Then
    Key := #0;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.OptWEBesAKTBestCloseCheckBoxClick(Sender: TObject);
begin
  if not(OptWEBesAKTBestCloseCheckBox.Checked) then
    OptWEPosAKTCheckBox.Enabled := True
  else begin
    OptWEPosAKTCheckBox.Checked := False;
    OptWEPosAKTCheckBox.Enabled := False;
  end;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.ArtComboBoxChange(Sender: TObject);
begin
  if (ArtComboBox.GetItemText <> 'SPERR') then
    SperrLagerComboBox.Enabled := True
  else begin
    SperrLagerComboBox.Enabled := False;
    SperrLagerComboBox.ItemIndex := 0;
  end;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// * Datum        : 04.11.2023
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.AutoCheckInvCheckBoxClick(Sender: TObject);
begin
  AutoInvArCheckBox.Enabled := AutoCheckInvCheckBox.Checked;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.FehlwareInvCheckBoxClick(Sender: TObject);
begin
  FehlwareCheckInvCheckBox.Enabled := not(FehlwareInvCheckBox.Checked);
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := False;

    if (NameEdit.Enabled and (Length(NameEdit.Text) = 0)) then begin
      MessageDLG(FormatMessageText(1734, []), mtError, [mbOk], 0);
      NameEdit.SetFocus;
    end
    else
      CanClose := True
  end;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.FormCreate(Sender: TObject);
begin
  NameEdit.Text := '';
  BeschreibungEdit.Text := '';
  BetriebEdit.Text := '';
  IFCIDEdit.Text := '';
  AdrEdit.Text := '';
  RoadEdit.Text := '';
  PLZEdit.Text := '';
  OrtEdit.Text := '';
  LandEdit.Text := '';
  ILNEdit.Text := '';
  EUNrEdit.Text := '';
  FonEdit.Text := '';
  FaxEdit.Text := '';
  ContactEdit.Text := '';
  MailEdit.Text := '';
  MaxInvMengeEdit.Text := '';
  NaheNullEdit.Text := '';

  QSWEComboBox.ItemIndex := -1;
  QSRETComboBox.ItemIndex := -1;

  PlanVollPALCheckBox.Checked := False;
  PlanNVEPalFaktorCheckBox.Checked := False;

  LVSSprachModul.InitForm(Self);

{$IFDEF TranslateHelper}
  LVSSprachModul.SetNoTranslate(Self, MandComboBox);
  LVSSprachModul.SetNoTranslate(Self, LocComboBox);
  LVSSprachModul.SetNoTranslate(Self, SperrLagerComboBox);
  LVSSprachModul.SetNoTranslate(Self, QSWEComboBox);
  LVSSprachModul.SetNoTranslate(Self, QSRETComboBox);
  LVSSprachModul.SetNoTranslate(Self, ArtComboBox);
  LVSSprachModul.SetNoTranslate(Self, HKLComboBox);
{$ENDIF}
  PageControl1.ActivePage := BestandTabSheet;
end;

// ******************************************************************************
// * Function Name:
// * Author       : Stefan Graf
// ******************************************************************************
// * Description
// ******************************************************************************
// * Return Value :
// ******************************************************************************
procedure TEditLagerForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects(MandComboBox);
  ClearComboBoxObjects(LocComboBox);
  ClearComboBoxObjects(SperrLagerComboBox);
  ClearComboBoxObjects(QSWEComboBox);
  ClearComboBoxObjects(QSRETComboBox);
  ClearComboBoxObjects(ArtComboBox);
  ClearComboBoxObjects(HKLComboBox);
end;

end.

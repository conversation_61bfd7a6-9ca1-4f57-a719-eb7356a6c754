unit ABCMainDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, ExtCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, ComCtrls,
  StdCtrls, ComboBoxPro, StringGridPro, Menus, Buttons, IntegerUpDown,
  BetterADODataSet, Vcl.DBCtrls
  ;

type
  TABCMainForm = class(TForm)
    UpperPanel: TPanel;
    LowerPanel: TPanel;
    Splitter1: TSplitter;
    PositionDBGrid: TDBGridPro;
    PositionButtonPanel: TPanel;
    ConfDBGrid: TDBGridPro;
    HeaderButtonPanel: TPanel;
    ConfDataSource: TDataSource;
    PositionDataSource: TDataSource;
    NewConfButton: TButton;
    EditConfButton: TButton;
    DeleteConfButton: TButton;
    DeleteArtikelButton: TButton;
    BottomPanel: TPanel;
    CloseButton: TButton;
    ConfQuery: TADOQuery;
    PositionQuery: TADOQuery;
    ReevaluateButton: TButton;
    SchwellenwerteGroupbox: TGroupBox;
    ASchwelleLabel: TLabel;
    BSchwelleLabel: TLabel;
    CSchwelleLabel: TLabel;
    SetKlasseButton: TButton;
    NewKopfButton: TButton;
    DeleteKopfButton: TButton;
    LowerPositionButtonPanel: TPanel;
    UpperPositionButtonPanel: TPanel;
    KopfQuery: TADOQuery;
    EditKopfButton: TButton;
    KopfComboBox: TComboBoxPro;
    KopfLabel: TLabel;
    PositionLabel: TLabel;
    ConfLabel: TLabel;
    BlockedTimer: TTimer;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure CloseButtonClick(Sender: TObject);
    procedure NewConfButtonClick(Sender: TObject);
    procedure EditConfButtonClick(Sender: TObject);
    procedure DeleteConfButtonClick(Sender: TObject);
    procedure FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
    procedure DeleteArtikelButtonClick(Sender: TObject);
    procedure ConfDataSourceDataChange(Sender: TObject; Field: TField);
    procedure ReevaluateButtonClick(Sender: TObject);
    procedure SetKlasseButtonClick(Sender: TObject);

    procedure RefreshKoepfe;
    procedure NewKopfButtonClick(Sender: TObject);
    procedure EditKopfButtonClick(Sender: TObject);
    procedure DeleteKopfButtonClick(Sender: TObject);
    procedure KopfComboBoxChange(Sender: TObject);
    procedure BlockedTimerTimer(Sender: TObject);
  private
    procedure SpamSafeLoading;
    procedure RefreshPositions;
    procedure RefreshConfigurations;

    procedure OpenConfEditForm(const RefConf : Integer = -1);
    procedure OpenKopfEditForm(const RefConf : Integer; const RefKopf : Integer = -1);
    procedure DisplaySchwellenwerte;
  end;

implementation

{$R *.dfm}

uses
  CommCtrl, Clipbrd, VCLUtilitys, FrontEndUtils, DatenModul, DBGridUtilModule, ConfigModul, LVSExport,
  ShellAPI, SprachModul, ResourceText, ABCConfEditDLG, ABCKopfEditDLG, LVSArtikelAbcInterface, FrontendMessages;


{ Buttons }

procedure TABCMainForm.NewConfButtonClick(Sender: TObject);
begin
  OpenConfEditForm;
end;

procedure TABCMainForm.EditConfButtonClick(Sender: TObject);
begin
  OpenConfEditForm(ConfQuery.FieldByName('REF').AsInteger);
end;

procedure TABCMainForm.DeleteConfButtonClick(Sender: TObject);
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1867, [ConfQuery.FieldByName('NAME').AsString]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    DeleteAbcConf(ConfQuery.FieldByName('REF').AsInteger);
    RefreshConfigurations;
  end;
end;

procedure TABCMainForm.NewKopfButtonClick(Sender: TObject);
begin
  OpenKopfEditForm(ConfQuery.FieldByName('REF').AsInteger);
end;

procedure TABCMainForm.EditKopfButtonClick(Sender: TObject);
begin
  OpenKopfEditForm(ConfQuery.FieldByName('REF').AsInteger, GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex));
end;

procedure TABCMainForm.DeleteKopfButtonClick(Sender: TObject);
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1868, [KopfComboBox.Items[KopfComboBox.ItemIndex]]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    DeleteAbcKopf(GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex));
    RefreshKoepfe;
  end;
end;

procedure TABCMainForm.ReevaluateButtonClick(Sender: TObject);
begin
  CreateAbcPositions(GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex));
  RefreshPositions;
end;

procedure TABCMainForm.SetKlasseButtonClick(Sender: TObject);
var
  I : Integer;
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1870, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    for I := 0 to PositionDBGrid.SelectedRows.Count - 1 do
    begin
      PositionQuery.GotoBookmark(TBookmark(PositionDBGrid.SelectedRows[I]));
      UpdateKlasse(PositionQuery.FieldByName('REF_KOPF').AsInteger, PositionQuery.FieldByName('REF_AR').AsInteger, PositionQuery.FieldByName('KLASSE').DisplayText[1]);
    end;
    RefreshPositions;
  end;
end;

procedure TABCMainForm.DeleteArtikelButtonClick(Sender: TObject);
var
  I : Integer;
begin
  if (FrontendMessages.MessageDlg(FormatMessageText(1869, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then
  begin
    for I := 0 to PositionDBGrid.SelectedRows.Count - 1 do
    begin
      PositionQuery.GotoBookmark(TBookmark(PositionDBGrid.SelectedRows[I]));
      DeleteAbcPosition(GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex), PositionQuery.FieldByName('REF_AR').AsInteger);
    end;
    ReevaluateResult(GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex));
    RefreshPositions;
  end;
end;

procedure TABCMainForm.CloseButtonClick(Sender: TObject);
begin
  self.Close;
end;

{ Events }

procedure TABCMainForm.BlockedTimerTimer(Sender: TObject);
begin
  BlockedTimer.Enabled := false;
  RefreshKoepfe;
  RefreshPositions;
end;

procedure TABCMainForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.ConfigDBGrids (Self);
    DBGridUtils.SetupDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, KopfComboBox);
    LVSSprachModul.SetNoTranslate (Self, ASchwelleLabel);
    LVSSprachModul.SetNoTranslate (Self, BSchwelleLabel);
    LVSSprachModul.SetNoTranslate (Self, CSchwelleLabel);
  {$endif}
end;

procedure TABCMainForm.FormShow(Sender: TObject);
begin
  RefreshConfigurations;
  RefreshKoepfe;
  RefreshPositions;
end;

procedure TABCMainForm.KopfComboBoxChange(Sender: TObject);
begin
  RefreshPositions;
end;

procedure TABCMainForm.FormKeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  if (Key = VK_F5) then
  begin
    RefreshConfigurations;
  end
  else if (Key = VK_ESCAPE) then
  begin
    self.Close;
  end;
end;

procedure TABCMainForm.ConfDataSourceDataChange(Sender: TObject; Field: TField);
begin
  SpamSafeLoading;
end;

procedure TABCMainForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  if Assigned (LVSConfigModul) then
    LVSConfigModul.SaveFormInfo (Self);
end;

{ Methods }

procedure TABCMainForm.DisplaySchwellenwerte;
const
  baseString : string = '%s: %d-%d';
var
  a,b,c : Integer;
begin
  PositionQuery.Close;
  PositionQuery.SQL.Text := 'SELECT MAX(RANKING), KLASSE FROM V_PCD_ARTIKEL_ABC_POS WHERE REF_KOPF = :refkopf AND SPRACHE = :language GROUP BY KLASSE ORDER BY MAX(RANKING)';
  PositionQuery.Parameters.ParamByName('refkopf').Value := GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex);
  PositionQuery.Parameters.ParamByName('language').Value := SprachModul.LVSSprachModul.AktSprache;
  PositionQuery.Active := True;

  a := PositionQuery.Fields.Fields[0].AsInteger;
  PositionQuery.Next;
  b := PositionQuery.Fields.Fields[0].AsInteger;
  PositionQuery.Next;
  c := PositionQuery.Fields.Fields[0].AsInteger;

  ASchwelleLabel.Caption := Format(baseString, ['A', 1, a]);
  BSchwelleLabel.Caption := Format(baseString, ['B', a+1, b]);
  CSchwelleLabel.Caption := Format(baseString, ['C', b+1, c]);
end;

procedure TABCMainForm.RefreshConfigurations;
begin
  ConfQuery.Close;
  ConfQuery.SQL.Text := 'SELECT * FROM V_PCD_ARTIKEL_ABC_CONF';
  ConfQuery.Active := True;
  ConfDBGrid.RefreshData;
  RefreshKoepfe;
end;

procedure TABCMainForm.RefreshKoepfe;
begin
  KopfQuery.Close;
  KopfQuery.SQL.Text := 'SELECT NAME, REF FROM V_PCD_ARTIKEL_ABC_KOPF WHERE REF_CONF = :ref_conf ORDER BY EXECUTE_DATE DESC';
  KopfQuery.Parameters.ParamByName('ref_conf').Value := ConfQuery.FieldByName('REF').AsInteger;
  KopfQuery.Active := True;
  KopfComboBox.Refresh;

  KopfComboBox.Items.Clear;
  if KopfQuery.RecordCount > 0 then
  begin
    while not KopfQuery.Eof do
    begin
      KopfComboBox.AddItem(KopfQuery.FieldByName('NAME').AsString, TComboBoxRef.Create(KopfQuery.FieldByName('REF').AsInteger));
      KopfQuery.Next;
    end;
    KopfComboBox.ItemIndex := 0;
  end
  else
  begin
    KopfComboBox.Text := '';
  end;
end;

procedure TABCMainForm.RefreshPositions;
begin
  DisplaySchwellenwerte;
  PositionQuery.Close;
  PositionQuery.SQL.Text := 'SELECT * FROM V_PCD_ARTIKEL_ABC_POS WHERE REF_KOPF = :refkopf AND SPRACHE = :language';
  PositionQuery.Parameters.ParamByName('refkopf').Value := GetComboBoxRef(KopfComboBox, KopfComboBox.ItemIndex);
  PositionQuery.Parameters.ParamByName('language').Value := SprachModul.LVSSprachModul.AktSprache;
  PositionQuery.Active := True;
  PositionDBGrid.RefreshData;
end;

procedure TABCMainForm.OpenConfEditForm(const RefConf : Integer = -1);
var
  AbcConfEditForm: TAbcConfEditForm;
begin
  AbcConfEditForm := TAbcConfEditForm.Create(self);
  AbcConfEditForm.Prepare(RefConf);
  if AbcConfEditForm.ShowModal = mrOk then
  begin

  end;
  AbcConfEditForm.Free;
end;

procedure TABCMainForm.OpenKopfEditForm(const RefConf, RefKopf: Integer);
var
  AbcKopfEditForm: TAbcKopfEditForm;
begin
  AbcKopfEditForm := TAbcKopfEditForm.Create(self);
  AbcKopfEditForm.Prepare(RefConf, RefKopf);
  if AbcKopfEditForm.ShowModal = mrOk then
  begin

  end;
  AbcKopfEditForm.Free;
end;

procedure TABCMainForm.SpamSafeLoading;
begin
  if BlockedTimer.Enabled then
  begin
    BlockedTimer.Enabled := false;
  end;
  BlockedTimer.Enabled := true;
end;


end.

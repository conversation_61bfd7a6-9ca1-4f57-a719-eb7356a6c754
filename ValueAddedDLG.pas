unit ValueAddedDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, ValueServiceFRM;

type
  TValueAddedForm = class(TForm)
    Panel1: TPanel;
    Label1: TLabel;
    Label2: TLabel;
    AuftragLabel: TLabel;
    KundeLabel: TLabel;
    Panel2: TPanel;
    OkButton: TButton;
    AbortButton: TButton;
    ScrollBox1: TScrollBox;
    PrintButton: TButton;
    Bevel1: TBevel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure PrintButtonClick(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefAufPos     : Integer;
    fRefAuftrag    : Integer;
    fRefMand       : Integer;
    fRefSubMAnd    : Integer;
    fRefLager      : Integer;

    fFrameList     : TList;

    fServiceCount     : Integer;
    fOpenServiceCount : Integer;

    procedure Prepare;
  public
    property ServiceCount     : Integer read fServiceCount;
    property OpenServiceCount : Integer read fOpenServiceCount;

    procedure PrepareWA (const RefAuftrag, RefAufPos : Integer);
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DB, ADODB, DatenModul, PrintModul, LVSDatenInterface, FrontendUtils, FrontendMessages, SprachModul, ResourceText,
  ErrorTracking;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  res     : Integer;
  idx     : Integer;
  frame   : TValueServiceFrame;
begin
  if AbortButton.Focused then
    CanClose := True
  else begin
    CanClose := false;

    idx := 0;
    res := 0;
    while (idx < fFrameList.Count) and (res = 0) do begin
      frame := TValueServiceFrame (fFrameList [idx]);

      if (frame.DoneCheckBox.Checked) then begin
        res := SetValueServiceDone (frame.REF, '');
      end;

      Inc (idx);
    end;

    if (res = 0) then
      CanClose := True
    else
      FrontendMessages.MessageDLG (FormatMessageText (1775, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOk], 0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.FormCreate(Sender: TObject);
begin
  KundeLabel.Caption   := '';
  AuftragLabel.Caption := '';

  fFrameList := TList.Create;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    //LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.FormDestroy(Sender: TObject);
begin
  fFrameList.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.Prepare;
var
  frame      : TValueServiceFrame;
  query      : TADOQuery;
begin
  fServiceCount := 0;
  fOpenServiceCount := 0;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefAufPos > 0) then begin
      query.SQL.Add ('select svr.*,ap.REF_AR,ap.ARTIKEL_NR,ap.ARTIKEL_TEXT from V_AUFTRAG_VALUE_SERVICE svr left outer join V_AUFTRAG_POS ap on (ap.REF=svr.REF_AUF_POS) where svr.REF_AUF_POS=:ref_auf_pos');
      query.Parameters.ParamByName('ref_auf_pos').Value := fRefAufPos;
    end else begin
      query.SQL.Add ('select svr.*,ap.REF_AR,ap.ARTIKEL_NR,ap.ARTIKEL_TEXT from V_AUFTRAG_VALUE_SERVICE svr left outer join V_AUFTRAG_POS ap on (ap.REF=svr.REF_AUF_POS) where svr.REF_AUF_KOPF=:ref_auf');
      query.Parameters.ParamByName('ref_auf').Value := fRefAuftrag;
    end;

    try
      query.Open;

      while not (query.Eof) do begin
        frame := TValueServiceFrame.Create (Self);
        frame.Name   := 'ServiceFrame_'+IntToStr (fServiceCount + 1);
        frame.Parent := ScrollBox1;
        frame.Align  := alTop;

        frame.Ref := query.FieldByName('REF').AsInteger;

        frame.ArtikelLabel.Caption     := query.FieldByName('ARTIKEL_NR').AsString + ' / ' + query.FieldByName('ARTIKEL_TEXT').AsString;
        frame.ServiceLabel.Caption     := query.FieldByName('SERVICE').AsString;
        frame.ServiceInfoLabel.Caption := query.FieldByName('SERVICE_INFO').AsString;

        if not query.FieldByName('DONE_DATE').IsNull then begin
          frame.DoneCheckBox.Visible := True;
        end else begin
          Inc (fOpenServiceCount);
        end;

        fFrameList.Add (frame);

        Inc (fServiceCount);

        query.Next;
      end;

      query.Close;
    except
      on e: Exception do begin
        ErrorTrackingModule.WriteErrorLog ('TValueAddedForm.Prepare', e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);
      end;
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.PrepareWA (const RefAuftrag, RefAufPos : Integer);
var
  query : TADOQuery;
begin
  fRefAuftrag := RefAuftrag;
  fRefAufPos  := RefAufPos;

  query := TADOQuery.Create (Self);
  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    if (fRefAufPos > 0) then begin
      query.SQL.Add ('select ap.REF_AR,ap.ARTIKEL_NR,ap.ARTIKEL_TEXT,auf.REF_MAND,auf.REF_SUB_MAND,auf.REF_LAGER,auf.AUFTRAG_NR,auf.KUNDEN_NAME from V_AUFTRAG_POS ap, V_AUFTRAG auf where ap.REF=:ref and auf.REF=ap.REF_AUF_KOPF');
      query.Parameters [0].Value := fRefAufPos;

      query.Open;

      fRefMand    := query.FieldByName('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldByName('REF_SUB_MAND'));
      fRefLager   := query.FieldByName('REF_LAGER').AsInteger;

      KundeLabel.Caption   := query.FieldByName('KUNDEN_NAME').AsString;
      AuftragLabel.Caption := query.FieldByName('AUFTRAG_NR').AsString;

      query.Close;

      Prepare;
    end else if (fRefAuftrag > 0) then begin
      query.SQL.Add ('select auf.REF_MAND,auf.REF_SUB_MAND,auf.REF_LAGER,auf.AUFTRAG_NR,auf.KUNDEN_NAME from V_AUFTRAG auf where auf.REF=:ref');
      query.Parameters [0].Value := fRefAuftrag;

      query.Open;

      fRefMand    := query.FieldByName('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (query.FieldByName('REF_SUB_MAND'));
      fRefLager   := query.FieldByName('REF_LAGER').AsInteger;

      KundeLabel.Caption   := query.FieldByName('KUNDEN_NAME').AsString;
      AuftragLabel.Caption := query.FieldByName('AUFTRAG_NR').AsString;

      query.Close;

      Prepare;
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TValueAddedForm.PrintButtonClick(Sender: TObject);
var
  res    : Integer;
  errmsg : String;
begin
  //Liste mit den Values added services drucken
  res := PrintModule.PreparePreview;

  if (res = 0) Then begin
    if (fRefSubMand > 0) then
      res := PrintModule.PrintReport('', fRefSubMand, fRefLager, '', 'VALUE_SERVICES_LIST', '', ['REF:' + IntToStr (fRefAuftrag)], errmsg, True, 0)
    else
      res := PrintModule.PrintReport('', fRefMand, fRefLager, '', 'VALUE_SERVICES_LIST', '', ['REF:' + IntToStr (fRefAuftrag)], errmsg, True, 0);

    if (res = 0) then
      PrintModule.BeginPreview;
  end;

  if (res <> 0) Then
    FrontendMessages.MessageDLG (FormatMessageText (1776, [errmsg]), mtError, [mbOK], 0);
end;

end.

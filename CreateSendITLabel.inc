﻿  //****************************************************************************
  //* Function Name:
  //* Author       : <PERSON>
  //****************************************************************************
  //* Description
  //****************************************************************************
  //* Return Value :
  //****************************************************************************
  function Create<PERSON>endITLabel (const <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, IDNummer, VersandStelle : String;
                              const <PERSON><PERSON><PERSON> : Double;
                              var   SendungsNr, TrackUrl, Barcode, LabelFormat : String;
                              LabelImage : TMemoryStream; var ErrorText : String) : Integer;
  const
  SCHENKER_SHORTCUT: string = 'SCH';
  var
    s,
    res,
    idx,
    stridx,
    sumvpe,
    strpos       : Integer;
    shipid,
    csvstr       : AnsiString;
    firstsvr,
    noneuflag    : boolean;
    numstr,
    optstr,
    arnr,
    versart,
    textstr,
    namestr,
    prtname,
    nrstr,
    idstr,
    taricstr,
    placestr,
    streetstr,
    streetaddstr,
    lnstr,
    wistr,
    histr,
    user,
    passwd,
    gwstr,
    ltstr,
    isostr,
    urlstr,
    apistr,
    telstr,
    mailstr,
    mailnotystr,
    phonenotystr,
    nvestr,
    retstr,
    kdaufnr,
    versartstr,
    notifystr,
    vornamestr,
    nachnamestr  : String;
    nettosum     : Double;
    aranz,
    portint      : Integer;
    fstream      : TFileStream;
    cfgquery     : TSmartQuery;
    spedquery    : TSmartQuery;
    gatequery    : TSmartQuery;
    selquery     : TSmartQuery;
    opt_ident    : String;
    opt_routing  : String;
    incstr       : String;
    dutaccstr    : String;
    delaccstr    : String;
    sendaccstr   : String;
    recvaccstr   : String;
    jsonstr      : String;
    resp         : String;
    errcode      : Integer;
    errtext      : String;
    lblstr       : AnsiString;
    sdata        : TMemoryStream;
    js           : TlkJSONobject;
    fs,
    us,
    errfs        : TlkJSONbase;

    {$ifdef UNICODE}
      utfstr       : AnsiString;
      datastr      : String;
    {$endif}

    csvwerte     : array [0..511] of AnsiString;
	
	


    function CheckCSVValue (const InsideName : String) : boolean;

    var
      idx     : Integer;
      suchtag : TXMLTagEntry;
      suchstr : String;
      idxstr  : String;
    begin
      Result := false;

      idxstr := '';

      suchtag := SendITMapping.FindTag ('fieldnameMapping');

      if Assigned (suchtag) then
        suchtag := suchtag.SubTag;

      while Assigned (suchtag) do begin
        suchstr := suchtag.GetAttribut ('inside');

        if (suchstr = InsideName) then
          break;

        suchtag := suchtag.NextSibling;
      end;

      if Assigned (suchtag) then begin
        idxstr := suchtag.GetAttribut ('outside');

        if (Length (idxstr) > 0) then begin
          if TryStrToInt (idxstr, idx) then
            Result := true;
        end;
      end;
    end;

    function SetCSVValue (const InsideName, Wert : String) : integer;
    var
      res,
      idx     : Integer;
      suchtag : TXMLTagEntry;
      suchstr : String;
      idxstr  : String;
    begin
      res := 0;

      idxstr := '';

      suchtag := SendITMapping.FindTag ('fieldnameMapping');

      if Assigned (suchtag) then
        suchtag := suchtag.SubTag;

      while Assigned (suchtag) do begin
        suchstr := suchtag.GetAttribut ('inside');

        if (suchstr = InsideName) then
          break;

        suchtag := suchtag.NextSibling;
      end;

      if Assigned (suchtag) then begin
        idxstr := suchtag.GetAttribut ('outside');

        if (Length (idxstr) > 0) then begin
          if TryStrToInt (idxstr, idx) then begin
            if (idx <= High (csvwerte)) then begin
              {$ifdef UNICODE}
                csvwerte [idx] := AnsiString (Wert);
                //csvwerte [idx] := UTF8Encode (Wert);
              {$else}
                csvwerte [idx] := Wert;
              {$endif}
            end else begin
              res := -35;
              ErrorText := 'SendIT CSV Mapping ist zu groß';
            end;
          end;
        end;
      end;

      Result := res;
    end;

  begin
    res := 0;
    SendungsNr  := '';
    TrackUrl    := '';
    Barcode     := '';
    LabelFormat := '';
    ErrorText   := '';
    LabelImage.Clear;

    strpos := Pos (';', PrtInfo.Leitstand);

    if (strpos > 0) then
      prtname := Copy (PrtInfo.Leitstand, 1, strpos - 1)
    else
      prtname := PrtInfo.Leitstand;

    versart := '';

    //Das wird die ShipmendID
    if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      shipid := query.FieldByName('KD_KOMM_NR').AsString
    else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      shipid := query.FieldByName('AUF_REFERENZ').AsString
    else if (Pos ('REF=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      shipid := query.FieldByName('KD_BESTELL_NR').AsString
    else if (Pos ('REF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
    else if (Pos ('REF=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
      csvstr := query.FieldByName('RECHNUNGS_NR').AsString
    else if query.FieldByName('AUSLIEFER_NR').IsNull then
      shipid := query.FieldByName('AUFTRAG_NR').AsString
    else
      shipid := query.FieldByName('AUSLIEFER_NR').AsString;

    //Bei OPT_MULTI_SHIPMEND=0 ist jedes Packstück eine eigen Sendungen
    if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
      //Die ShipmendID muss hierbei eindeutig sein
      if (Length (shipid) > 16) then
        shipid := copy (shipid, Length (shipid) - 16) + '-' + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) - 3)
      else if (Length (shipid) > 14) then
        shipid := shipid + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) + 1 - (20 - Length (shipid)))
      else
        shipid := shipid + '-' + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) - 5);
    end;

    if (LabelType = 'Return') then
      shipid := 'R' + shipid;

    streetaddstr := '';

    if (Assigned (liefquery.FindField('HOUSE_NO')) and not liefquery.FieldByName('HOUSE_NO').IsNull) then begin
      nrstr      := liefquery.FieldByName('HOUSE_NO').AsString;
      streetstr := StringReplace (liefquery.FieldByName('STRASSE_NO_HOUSE_NO').AsString, ';', ',', [rfReplaceAll]);

      //Zweiter Teil der Strasse
      if not (liefquery.FieldByName('STRASSE_2').IsNull) then begin
        if (copy (Versender, 1, 3) = 'HVS') and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
          //Bei Hermes gibt es keinen zweiten Teil der Strasse, somit muss das angehangen werden
          streetstr := streetstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
        else
          streetaddstr := ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]));
      end;
    end else begin
      //Strasse und Hausnummer ggf. trennen
      nrstr      := '';
      streetstr := StringReplace (liefquery.FieldByName('STRASSE').AsString, ';', ',', [rfReplaceAll]);

      idx := Length (streetstr);
      while (idx > 1) and (streetstr [idx] <> ' ') do
        Dec (idx);

      if (idx > 1) then begin
        nrstr := copy (streetstr, idx + 1);

        //Bei Hermes kann die Hausnummer maximal 4 Zeichne lange sein, daher dürfen längere Hausnummern nicht abgetrennt werden
        if (copy (Versender, 1, 3) = 'HVS') and (Length (nrstr) > 4) then
          nrstr := ''
        else
          streetstr := Copy (streetstr, 1, idx);
      end;

      //Zweiter Teil der Strasse
      if not (liefquery.FieldByName('STRASSE_2').IsNull) then begin
        //Kurze Zusätze wie Hausnummer usw. wird an die Strasse angehangen
        if (Length (liefquery.FieldByName('STRASSE_2').AsString) <= 4) then
          streetstr := streetstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
        else if (copy (Versender, 1, 3) = 'HVS') and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
          //Bei Hermes gibt es keinen zweiten Teil der Strasse, somit muss das angehangen werden
          streetstr := streetstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
        else
          streetaddstr := ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]));
      end;
    end;

    if liefquery.FieldByName('NACHNAME').IsNull then begin
      namestr := Trim (liefquery.FieldByName ('NAME1').AsString);

      idx := Length (namestr);

      //Das erste Leerzeichen nach dem Nachnamen finden
      while (idx > 1) and (namestr [idx] <> ' ') do
        Dec (idx);

      if (idx > 1) then begin
        nachnamestr := copy (namestr, idx + 1);

        //Leerzeichen zwischen Vor- und Nachname entfernen
        while (idx > 1) and (namestr [idx] = ' ') do
          Dec (idx);

        vornamestr := copy (namestr, 1, idx);
      end else begin
        vornamestr  := '';
        nachnamestr := namestr;
      end;
    end else begin
      vornamestr := liefquery.FieldByName('VORNAME').AsString;
      nachnamestr := liefquery.FieldByName('NACHNAME').AsString;
    end;

    if (liefquery.FieldByName('LAND').IsNull) and (liefquery.FieldByName('LAND_ISO').IsNull) then
      isostr := 'DE'
    else if (liefquery.FieldByName('LAND_ISO').IsNull) then
      isostr := UpperCase (StringReplace (liefquery.FieldByName('LAND').AsString, ';', ',', [rfReplaceAll]))
    else
      isostr := liefquery.FieldByName('LAND_ISO').AsString;

    if (Length (IDNummer) > 0) then
      nvestr := IDNummer
    else
      nvestr := query.FieldByName('NVE_NR').AsString;

    if (LabelType = 'Return') then begin
      if (query.FieldByName ('RETOUREN_NR').IsNull) then
        idstr := 'ret_'+query.FieldByName ('AUFTRAG_NR').AsString
      else
        idstr := 'ret_'+query.FieldByName ('RETOUREN_NR').AsString
    end else begin
      idstr := nvestr;
    end;

    noneuflag := not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString);

    if not (liefquery.FieldByName('TELEFON').IsNull) then
      telstr := StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
    else if not (adrquery.FieldByName('TELEFON').IsNull) then
      telstr := StringReplace (adrquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
    else
      telstr := StringReplace (query.FieldByName('DEFAULT_PHONE_NUMBER').AsString, ';', ',', [rfReplaceAll]);

    notifystr    := '';
    mailnotystr  := '';
    phonenotystr := '';

    gatequery  := TSmartQuery.Create (Nil);
    spedquery  := TSmartQuery.Create (Nil);

    try
      gatequery.ReadOnly := True;
      gatequery.Session := Query.Session;

      spedquery.ReadOnly := True;
      spedquery.Session := Query.Session;

      gatequery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
      gatequery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

      gatequery.Open;

      //Hier werden die Daten für Mial und Notification nach dem älteren Verfahren ermittelt
      if Assigned (gatequery.FindField ('NOTIFICATION_BY_MAIL')) then begin
        if (gatequery.FieldByName('NOTIFICATION_BY_MAIL').AsString > '0') then begin
          if not liefquery.FieldByName('EMAIL').IsNull then
            mailstr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
          else if not adrquery.FieldByName('EMAIL').IsNull then
            mailstr := StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
          else
            mailstr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]);

          if (Length (mailstr) > 0) then
            notifystr := 'MAIL';
        end;
      end else if kepemail then begin
        if not liefquery.FieldByName('EMAIL').IsNull then
          mailstr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
        else if not adrquery.FieldByName('EMAIL').IsNull then
          mailstr := StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
        else
          mailstr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]);

        if (Length (mailstr) > 0) then
          notifystr := 'MAIL';
      end;

      selquery  := TSmartQuery.Create (Nil);
      try
        selquery.ReadOnly := True;
        selquery.Session := Query.Session;

        selquery.SQL.Clear;
        selquery.SQL.Add ('select * from V_AUFTRAG_VERSAND where REF_AUF_KOPF=:ref');
        selquery.Params [0].Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

        try
          selquery.Open;

          //Das ist die neue Art der Notification-Steuerung (WALSER, PETROMAX und weitere)
          if (
              (Assigned (selquery.FindField ('OPT_AVIS_ART')) and not (selquery.FindField ('OPT_AVIS_ART').IsNull))
             or
              (Assigned (selquery.FindField ('DELIVERY_NOTIFICATION')) and not (selquery.FindField ('DELIVERY_NOTIFICATION').IsNull))
             )
          then begin
            mailstr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]);

            //OPT_AVIS_ART ist die neueste Art (PETROMAX)
            if (Assigned (selquery.FindField ('OPT_AVIS_ART')) and not (selquery.FindField ('OPT_AVIS_ART').IsNull)) then
              notifystr := selquery.FindField ('OPT_AVIS_ART').AsString
            else
              notifystr := selquery.FindField ('DELIVERY_NOTIFICATION').AsString;

            if (Pos ('MAIL', notifystr) > 0) then begin
              if not liefquery.FieldByName('EMAIL').IsNull then
                mailnotystr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
              else if not adrquery.FieldByName('EMAIL').IsNull then
                mailnotystr := StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
              else
                mailnotystr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]);
            end;

            if ((Pos ('PHONE', notifystr) > 0) or (Pos ('TELEFON', notifystr) > 0)) then begin
              if not (liefquery.FieldByName('TELEFON').IsNull) then
                phonenotystr := StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
              else if not (adrquery.FieldByName('TELEFON').IsNull) then
                phonenotystr := StringReplace (adrquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
              else
                phonenotystr := StringReplace (query.FieldByName('DEFAULT_PHONE_NUMBER').AsString, ';', ',', [rfReplaceAll]);
            end;
          end;

          selquery.Close;
        except
        end;
      finally
        selquery.Free;
      end;

      spedquery.SQL.Add ('select cfg.* from V_SPED_CONFIG cfg where cfg.REF_SPED=:ref');
      spedquery.Params [0].Value := query.FieldByName('REF_SPED').AsInteger;

      spedquery.Open;

      if Assigned (spedquery.FindField ('VERSAND_ART')) then begin
        if (Length (versartstr) > 0) then versartstr := versartstr + ';';

        versartstr := versartstr + spedquery.FindField ('VERSAND_ART').AsString;
      end;

      if (Pos ('MAIL', notifystr) > 0) then begin
        if (Length (mailnotystr) = 0) then begin
          if Assigned (gatequery.FindField ('NOTIFICATION_BY_MAIL')) then begin
            if (gatequery.FieldByName('NOTIFICATION_BY_MAIL').AsString > '0') then begin
              if not liefquery.FieldByName('EMAIL').IsNull then
                mailnotystr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
              else if not adrquery.FieldByName('EMAIL').IsNull then
                mailnotystr := StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
              else
                mailnotystr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]);
            end;
          end else begin
            if not liefquery.FieldByName('EMAIL').IsNull then
              mailnotystr := StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
            else if not adrquery.FieldByName('EMAIL').IsNull then
              mailnotystr := StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll])
            else
              mailnotystr := StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll]);
          end;
        end;
      end;

      lnstr := '';
      wistr := '';
      histr := '';

      if not (Assigned (spedquery.FindField ('OPT_DIM_SEND'))) or (spedquery.FieldByName ('OPT_DIM_SEND').AsString = '1') then begin
        lnstr := '15';
        wistr := '15';
        histr := '11';

        (*Das klappte bei DHL nicht, muss geprüft werden, daher erst mal ohne Abmessungen, wird nur bei DPD - Kleinpaketerkennung - benötigt*)
        if (Pos ('Return', Versender) > 0) then begin
          //Bei Retouren nur die minimale Paketgrösse angeben
        end else if (copy (Versender, 1, 3) = 'DPD') then begin
          if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
            if not (query.FieldByName('VOLUMEN').IsNull) then begin
              s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

              lnstr := IntToStr (s);
              wistr := IntToStr (s);
              histr := IntToStr (s);
            end;
          end else begin
            lnstr := IntToStr (Round (query.FieldByName('L').AsInteger / 10));
            wistr := IntToStr (Round (query.FieldByName('B').AsInteger / 10));
            histr := IntToStr (Round (query.FieldByName('H').AsInteger / 10));
          end;
        end else if (query.FieldByName('OPT_DIM_REQUIRED').AsString = '1') then begin
          if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
            if not (query.FieldByName('VOLUMEN').IsNull) then begin
              s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

              lnstr := IntToStr (s);
              wistr := IntToStr (s);
              histr := IntToStr (s);
            end;
          end else if ((Int64 (query.FieldByName('L').AsInteger) * Int64 (query.FieldByName('B').AsInteger) * Int64 (query.FieldByName('H').AsInteger)) > 50*50*50) then begin
            lnstr := IntToStr (Round (query.FieldByName('L').AsInteger / 10));
            wistr := IntToStr (Round (query.FieldByName('B').AsInteger / 10));
            histr := IntToStr (Round (query.FieldByName('H').AsInteger / 10));
          end;
        end else if not (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
          if (Versender = 'DHL') then begin
            //Beim DHL Standardpaket darf die Packgrösse nicht kleiner als 15x15x11 cm sein
            if (query.FieldByName('L').AsInteger < 150) then
              lnstr := '15'
            else
              lnstr := IntToStr (Round (query.FieldByName('L').AsInteger / 10));

            if (query.FieldByName('B').AsInteger < 150) then
              wistr :=  '15'
            else
              wistr :=  IntToStr (Round (query.FieldByName('B').AsInteger / 10));

            if (query.FieldByName('H').AsInteger < 110) then
              histr :=  '11'
            else
              histr := IntToStr (Round (query.FieldByName('H').AsInteger / 10));
          end else begin
            lnstr := IntToStr (Round (query.FieldByName('L').AsInteger / 10));
            wistr := IntToStr (Round (query.FieldByName('B').AsInteger / 10));
            histr := IntToStr (Round (query.FieldByName('H').AsInteger / 10));
          end;
        end;
      end;

      //Bei Geiss immer CT
      //if (copy (Versender, 1, 4) = 'GEIS') then
      //  ltstr := 'CT'
      //else
      if (query.FieldByName('EDI_CODE').IsNull) then
        ltstr := 'KT'
      else if ((copy (Versender, 1, 3) = 'HVS') or (copy (UpperCase (Versender), 1, 9) = 'HERMESAPI')) then begin
        ltstr := 'KT';
      end else if (copy (Versender, 1, 3) = 'SCH') then begin
        //Für DB Schenker
        if (query.FieldByName('EDI_CODE').AsString = '201') then
          ltstr := 'FP'
        else if (query.FieldByName('EDI_CODE').AsString = 'CP2') then
          ltstr := 'FP'
        else
          ltstr := query.FieldByName('EDI_CODE').AsString;
      end else
        ltstr := query.FieldByName('EDI_CODE').AsString;


      gwstr := '';

      if not (Assigned (spedquery.FindField ('OPT_WEIGHT_SEND'))) or (spedquery.FieldByName ('OPT_WEIGHT_SEND').AsString = '1') then begin
        //Das Gesamtgewicht der Sendung   - 16.06.2025: Obsolet
//        if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '1') then
//          gwstr := query.FieldByName('SENDUNG_BRUTTO').AsString
//        else
        gwstr := query.FieldByName('BRUTTO_GEWICHT').AsString;
      end;

      if Assigned (gatequery.FindField ('LEITSTAND_SUFFIX')) then
        prtname := prtname + gatequery.FieldByName ('LEITSTAND_SUFFIX').AsString;

      urlstr  := '';
      portint := -1;

      if (Length (PrtInfo.Setting) > 0) and (copy (PrtInfo.Setting, 1, 7) = 'http://') then
        urlstr := PrtInfo.Setting
      else if (Assigned (gatequery.FindField ('REST_URL')) and not gatequery.FieldByName ('REST_URL').IsNull) then
        urlstr := gatequery.FieldByName ('REST_URL').AsString
      else if (Assigned (spedquery.FindField ('REST_URL')) and not spedquery.FieldByName ('REST_URL').IsNull) then
        urlstr := spedquery.FieldByName ('REST_URL').AsString
      else
        urlstr := SendITServerURL;

      if (Length (urlstr) > 0) then begin
        //Portnummer ermitteln
        stridx := Length (urlstr);
        while ((stridx > 1) and (urlstr [stridx] <> '/') and (urlstr [stridx] <> ':')) do
          dec (stridx);

        if (stridx > 1) and (urlstr [stridx] = ':') then begin
          if not TryStrToInt (copy (urlstr, stridx + 1), portint) then
            portint := -1;

          urlstr := copy (urlstr, 1, stridx - 1);
        end;
      end;

      if (Length (urlstr) > 0) then begin
        user := 'testuser';
        passwd := 'testpw';

        if (Art = 'Reprint') then begin
          apistr := 'api/v2/package/reprint';

          jsonstr := '{';

          jsonstr := jsonstr + '"webServiceUser"'+':'+'"'+user+'"';
          jsonstr := jsonstr + ',"webServicePassword"'+':'+'"'+passwd+'"';

          jsonstr := jsonstr + ',"ClientID"'+':'+'"'+Absender+'"';
          jsonstr := jsonstr + ',"PackageID"'+':'+'"'+idstr+'"';

          jsonstr := jsonstr + ',"StationID"'+':'+'"'+prtname+'"';

          if ((PrtInfo.Name = 'SENDIT_DUMP') or (PrtInfo.Port = 'dump')) then
            jsonstr := jsonstr + ',"PrintMode"'+':'+'"PrnResponse"'
          else
            jsonstr := jsonstr + ',"PrintMode"'+':'+'"Device"';

          jsonstr := jsonstr + '}';
        end else begin
          apistr := 'api/v2/package/print';

          jsonstr := '{';

          jsonstr := jsonstr + '"webServiceUser"'+':'+'"'+user+'"';
          jsonstr := jsonstr + ',"webServicePassword"'+':'+'"'+passwd+'"';

          jsonstr := jsonstr + ',"shipmentID"'+':'+'"'+shipid+'"';

          if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
            jsonstr := jsonstr + ',"multiColli"'+':'+'"false"';
            jsonstr := jsonstr + ',"consolidatedDelivery"'+':'+'"false"';
          end else begin
            jsonstr := jsonstr + ',"multiColli"'+':'+'"true"';
            jsonstr := jsonstr + ',"consolidatedDelivery"'+':'+'"false"';
          end;

          jsonstr := jsonstr + ',"StationID"'+':'+'"'+prtname+'"';
          jsonstr := jsonstr + ',"ClientID"'+':'+'"'+Absender+'"';
          jsonstr := jsonstr + ',"UserID"'+':'+'"'+LabelUserNumID+'"';
          jsonstr := jsonstr + ',"ShippingType"'+':'+'"'+Versender+'"';
          jsonstr := jsonstr + ',"ShippingLocationID"'+':'+'"'+VersandStelle+'"';

          if ((PrtInfo.Name = 'SENDIT_DUMP') or (PrtInfo.Port = 'dump')) then
            jsonstr := jsonstr + ',"PrintMode"'+':'+'"PrnResponse"'
          else
            jsonstr := jsonstr + ',"PrintMode"'+':'+'"Device"';

          (*DHL 2MH, wird aber erst mal fix in MappingWebApiV1.xml gesetzt
          if (true) then begin
            jsonstr := jsonstr + ',"CustomerText1"'+':'+'"ZH"';
            jsonstr := jsonstr + ',"CustomerText2"'+':'+'"FV"';
          end;
          *)

          jsonstr := jsonstr + ',"ReferenceNumbers": {';
            if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
              csvstr := query.FieldByName('KD_KOMM_NR').AsString
            else if (Pos ('REF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
              csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
            else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
              csvstr := query.FieldByName('AUF_REFERENZ').AsString
            else csvstr := query.FieldByName('AUFTRAG_NR').AsString;

            jsonstr := jsonstr + '"Order"'+':'+'"'+csvstr+'"';

            if not (query.FieldByName('KD_AUFTRAG_NR').IsNull) then
              jsonstr := jsonstr + ',"CustomerOrder"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('KD_AUFTRAG_NR').AsString)+'"'
            else
              jsonstr := jsonstr + ',"CustomerOrder"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('KD_BESTELL_NR').AsString)+'"';

            jsonstr := jsonstr + ',"CustomerAccount"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('KUNDEN_NR').AsString)+'"';

            if not (query.FieldByName('LIEFERSCHEIN_NR').IsNull) then
              jsonstr := jsonstr + ',"DeliveryNote"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('LIEFERSCHEIN_NR').AsString)+'"';

            if not (query.FieldByName('RECHNUNGS_NR').IsNull) then
              jsonstr := jsonstr + ',"Invoice"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'"'
            else
              jsonstr := jsonstr + ',"Invoice"'+':'+'"'+csvstr+'"';

          jsonstr := jsonstr + '}';

          //Absender Adresse übergeben
          if (Pos ('SENDIT_ABSENDER', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then begin
            ;
          end else if not (AktSendITIFCAbsender) or (Pos ('Return', Versender) > 0) then begin
            ;
          end else begin
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Bei Dropship wird die Traderadresse angezeigt
              cfgquery.SQL.Add ( 'select'
                                +'  tadr.REF as REF,'
                                +'  translate (tadr.NAME1 using CHAR_CS) as TRADER_NAME,'
                                +'  translate (tadr.NAMEZUSATZ using CHAR_CS) as TRADER_NAMEZUSATZ,'
                                +'  coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME) as ABSENDER,'
                                +'  ml.ABSENDER_ZUSATZ,'
                                +'  coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                +'  coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                +'  coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                +'  coalesce (translate (tadr.LAND_ISO using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                +'  coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON,'
                                +'  tadr.EMAIL as Email'
                                +' from'
                                +'  V_AUFTRAG auf'
                                +'  inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                +'  inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                +'  left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                +'  left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                +' where auf.REF=:ref_auf'
                                );

              cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              if (cfgquery.FieldByName('TRADER_NAME').IsNull and cfgquery.FieldByName('ABSENDER').IsNull) then begin
                ;
              end else begin
                jsonstr := jsonstr + ',"ShipFrom": {';

                  if not (cfgquery.FieldByName('TRADER_NAME').IsNull) then begin
                    jsonstr := jsonstr + '"Address1"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TRADER_NAME').AsString)+'"';
                    jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('TRADER_NAMEZUSATZ').AsString)+'"';
                  end else begin
                    jsonstr := jsonstr + '"Address1"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ABSENDER').AsString)+'"';
                    jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ABSENDER_ZUSATZ').AsString)+'"';
                  end;

                  jsonstr := jsonstr + ',"Street"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('STRASSE').AsString)+'"';
                  jsonstr := jsonstr + ',"PostalCode"'+':'+'"'+cfgquery.FieldByName('PLZ').AsString+'"';
                  jsonstr := jsonstr + ',"City"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('ORT').AsString)+'"';
                  jsonstr := jsonstr + ',"Country"'+':'+'"'+cfgquery.FieldByName('LAND').AsString+'"';
                  jsonstr := jsonstr + ',"Phone"'+':'+'"'+cfgquery.FieldByName('TELEFON').AsString+'"';

                jsonstr := jsonstr + '}';
              end;
            finally
              cfgquery.Free;
            end;
          end;


          jsonstr := jsonstr + ',"ShipTo": {';
            jsonstr := jsonstr + '"FirstName"'+':'+'"'+ConvertJSONSonderzeichen (vornamestr)+'"';
            jsonstr := jsonstr + ',"LastName"'+':'+'"'+ConvertJSONSonderzeichen (nachnamestr)+'"';

            if (copy (Versender, 1, 3) = 'PFR') then begin
              //Bei Colissimo muss ShipToAdresse1 der Kundennamen udn ShipToAdresse2 die Firma sein
              if (liefquery.FieldByName('COMPANY').IsNull) then begin
                //ShipToAdresse1
                jsonstr := jsonstr + ',"Address1"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME1').AsString)+'"';

                //ShipToAdresse2
                if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                  jsonstr := jsonstr + ',"Address1"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME2').AsString)+'"'
                else if (Length (streetaddstr) > 0) then
                 //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
                  jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (streetaddstr)+'"';
              end else begin
                //ShipToAdresse1, bei uns in dem Falle dann NAME2
                jsonstr := jsonstr + ',"Address1"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME2').AsString)+'"';

                //ShipToAdresse2
                jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('COMPANY').AsString)+'"';
              end;
            end else begin
              //ShipToAdresse1
              jsonstr := jsonstr + ',"Address1"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME1').AsString)+'"';

              if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAME2').AsString)+'"'
              else if (Length (streetaddstr) > 0) then
               //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
                jsonstr := jsonstr + ',"Address2"'+':'+'"'+ConvertJSONSonderzeichen (streetaddstr)+'"'
              else if (copy (Versender, 1, 3) = 'HVS') then
                //Hermes benötig hier immer einen Text
                jsonstr := jsonstr + ',"Address2"'+':'+'"'+'-'+'"';
            end;

            if not (liefquery.FieldByName('ANSPRECHPARTNER').IsNull) then
            begin
              if (copy(Versender, 1, 3) = SCHENKER_SHORTCUT) then
                csvstr := ConvertJSONSonderzeichen(StringReplace(copy(liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else
                csvstr := ConvertJSONSonderzeichen(StringReplace(copy(liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 35), ';', ',', [rfReplaceAll]));
            end
            else if not (adrquery.FieldByName('ANSPRECHPARTNER').IsNull) then
            begin
              if (copy(Versender, 1, 3) = SCHENKER_SHORTCUT) then
                csvstr := ConvertJSONSonderzeichen(StringReplace(copy(adrquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else
                csvstr := ConvertJSONSonderzeichen(StringReplace(adrquery.FieldByName('ANSPRECHPARTNER').AsString, ';', ',', [rfReplaceAll]));
            end
            else if not (liefquery.FieldByName('LAND_ISO').IsNull or (liefquery.FieldByName('LAND_ISO').AsString = 'DE')) then
            begin
              csvstr := ConvertJSONSonderzeichen(StringReplace(copy(liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]));
            end
            else if ((copy(Versender, 1, 3) = SCHENKER_SHORTCUT)) then
            begin
              csvstr := ConvertJSONSonderzeichen(StringReplace(copy(liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]));
            end;


            jsonstr := jsonstr + ',"ContactName"'+':'+'"'+ csvstr +'"';

            //Bei Hermes gib es keine dritte Adresszeile
            if not (copy (Versender, 1, 3) = 'HVS') then begin
              if (Length (liefquery.FieldByName('NAMEZUSATZ').AsString) > 0) then
                jsonstr := jsonstr + ',"Address3"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('NAMEZUSATZ').AsString)+'"'
              else if (Length (streetaddstr) > 0) and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse3
                jsonstr := jsonstr + ',"Address3"'+':'+'"'+ConvertJSONSonderzeichen (streetaddstr)+'"';
            end;

            jsonstr := jsonstr + ',"Street"'+':'+'"'+ConvertJSONSonderzeichen (streetstr)+'"';
            jsonstr := jsonstr + ',"HouseNr"'+':'+'"'+ConvertJSONSonderzeichen (nrstr)+'"';
            jsonstr := jsonstr + ',"Country"'+':'+'"'+isostr+'"';

            //Für UPS wird der State-Code für US benötigt.
            if Assigned (liefquery.FindField ('STATE')) and not (liefquery.FieldByName('STATE').IsNull) then
              jsonstr := jsonstr + ',"StateOrProvinceCode"'+':'+'"'+liefquery.FieldByName('STATE').AsString+'"';

            jsonstr := jsonstr + ',"PostalCode"'+':'+'"'+liefquery.FieldByName('PLZ').AsString+'"';
            jsonstr := jsonstr + ',"City"'+':'+'"'+ConvertJSONSonderzeichen (liefquery.FieldByName('ORT').AsString)+'"';
            jsonstr := jsonstr + ',"Phone"'+':'+'"'+ConvertJSONSonderzeichen (telstr)+'"';
            jsonstr := jsonstr + ',"Mail"'+':'+'"'+ConvertJSONSonderzeichen (mailstr)+'"';
          jsonstr := jsonstr + '}';

          jsonstr := jsonstr + ',"Services": [';

          firstsvr := true;

          if (Pos ('fragile', lowercase (versartstr)) > 0) then begin
            if not firstsvr then begin
              jsonstr := jsonstr + ',';
            end;

            firstsvr := false;

            jsonstr := jsonstr + '{';
            jsonstr := jsonstr + '"type":"Fragile","value":"true"';
            jsonstr := jsonstr + '}';
          end;


          if (copy (Versender, 1, 6) = 'SCH006') then begin
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Add ( 'select'
                                +'  auf.REF as REF,'
                                +'  auf.KD_ANLIEFER_DATUM as KD_ANLIEFER_DATUM'
                                +' from'
                                +'  VQ_AUFTRAG auf'
                                +' where auf.REF=:ref_auf'
                                );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            try
              cfgquery.Open;

              idx := 1;

              while not (cfgquery.Eof) do begin
                if not (cfgquery.Fields [0].IsNull) and (cfgquery.Active) then begin
                  if not firstsvr then begin
                    jsonstr := jsonstr + ',';
                  end;

                  firstsvr := false;

                  jsonstr := jsonstr + '{';
                  if Assigned(cfgquery.FindField('KD_ANLIEFER_DATUM')) and not cfgquery.FieldByName('KD_ANLIEFER_DATUM').IsNull then
                    jsonstr := jsonstr + '"type":"Express","day":"' + FormatDateTime('yyyy-MM-dd', cfgquery.FieldByName('KD_ANLIEFER_DATUM').AsDateTime) + '"'
                  else if Assigned(gatequery.FindField('LIEFER_DATUM')) and not gatequery.FieldByName('LIEFER_DATUM').IsNull then
                    jsonstr := jsonstr + '"type":"Express","day":"' + FormatDateTime('yyyy-MM-dd', gatequery.FieldByName('LIEFER_DATUM').AsDateTime) + '"';
                  jsonstr := jsonstr + '}';


                  Inc (idx);
                end;

                cfgquery.Next;
              end;

              cfgquery.Close;
            except
            end;
          finally
            cfgquery.Free;
          end;
          end;



          if Assigned (gatequery.FindField ('INCOTERM')) and not (gatequery.FieldByName ('INCOTERM').IsNull) then begin
            if not firstsvr then begin
              jsonstr := jsonstr + ',';
            end;

            firstsvr := false;

            jsonstr := jsonstr + '{';
            jsonstr := jsonstr + '"type":"Incoterm","value":"'+gatequery.FieldByName ('INCOTERM').AsString+'"';
            jsonstr := jsonstr + '}';
          end;

        if ((copy(Versender, 1, 3) = SCHENKER_SHORTCUT)) and (notifystr = '@PLATFORM') then
         begin
            if not firstsvr then begin
                jsonstr := jsonstr + ',';
              end;

              firstsvr := false;

              jsonstr := jsonstr + '{';
              jsonstr := jsonstr + '"type":"Advice1","value":"'+LowerCase(notifystr)+'"';
              jsonstr := jsonstr + '}';
         end;


        // Wenn OPT_FILIAL_ROUTING 1 ist
        if (Assigned(spedquery.FindField('OPT_FILIAL_ROUTING'))) and
           not spedquery.FieldByName('OPT_FILIAL_ROUTING').IsNull and
           (spedquery.FieldByName ('OPT_FILIAL_ROUTING').AsString > '0')
        then
        begin
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Add ( 'select'
                                +'  tadr.REF as REF,'
                                +'  tadr.EMAIL as Email'
                                +' from'
                                +'  V_AUFTRAG auf'
                                +'  left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF)'
                                +' where auf.REF=:ref_auf'
                                );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            try
              cfgquery.Open;

              idx := 1;

              while not (cfgquery.Eof) do begin
                if not (cfgquery.Fields [0].IsNull) and (cfgquery.Active and not cfgquery.FieldByName('EMAIL').IsNull) then begin
                  if not firstsvr then begin
                    jsonstr := jsonstr + ',';
                  end;

                  firstsvr := false;

                  jsonstr := jsonstr + '{';
                  jsonstr := jsonstr + '"type"'+':'+'"AdvanceInstructions"';
                  jsonstr := jsonstr + ',"email"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.FieldByName('EMAIL').AsString)+'"';
                  jsonstr := jsonstr + '}';

                  Inc (idx);
                end;

                cfgquery.Next;
              end;

              cfgquery.Close;
            except
            end;
          finally
            cfgquery.Free;
          end;

          end;

          //Unterschrifts-Option
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Clear;
            cfgquery.SQL.Add( 'select nvl (prod.OPT_IDENTIFICATION,cfg.OPT_IDENTIFICATION)'
                             +' from V_SPED_CONFIG cfg'
                             +' left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod)'
                             +' where cfg.REF_SPED=:ref_sped'
                            );
            cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
            cfgquery.Params.ParamByName ('ref_prod').Value := query.FieldByName ('REF_SPED_PRODUKT').AsInteger;

            try
              cfgquery.Open;

              opt_ident := cfgquery.Fields [0].AsString;

              cfgquery.Close;
            except
              opt_ident := '';
            end;

            if ((Length (opt_ident) > 0) and (Pos ('1', opt_ident) > 0)) then begin
              if not firstsvr then begin
                jsonstr := jsonstr + ',';
              end;

              firstsvr := false;

              //Das ist für UPS +18
              if (copy (opt_ident, 1, 1) = '1') then begin //Signature +  SignatureAdult für UPS
                jsonstr := jsonstr + '{';
                jsonstr := jsonstr + '"type"'+':'+'"Signature"';
                jsonstr := jsonstr + '}';
              end;
            end;

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

          //Notification per Mail oder Telefon
        if (Length(notifystr) > 0) and
         ((Pos('MAIL', notifystr) > 0) or
          (Pos('PHONE', notifystr) > 0) or
          (Pos('TELEFON', notifystr) > 0)) then
        begin
            if not firstsvr then jsonstr := jsonstr + ',';
            firstsvr := false;

            jsonstr := jsonstr + '{';
            jsonstr := jsonstr + '"type"'+':'+'"notification"';

            if (Pos ('MAIL', notifystr) > 0) then
              jsonstr := jsonstr + ',"email"'+':'+'"'+ConvertJSONSonderzeichen (mailnotystr)+'"';

            if ((Pos ('PHONE', notifystr) > 0) or (Pos ('TELEFON', notifystr) > 0)) then
              jsonstr := jsonstr + ',"phone"'+':'+'"'+ConvertJSONSonderzeichen (phonenotystr)+'"';

            jsonstr := jsonstr + '}';
          end;

          //DHL 2MH Anweisungen
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Add ('select'
                             +'   case when arq.VERSAND_ART=''~'' then null else arq.VERSAND_ART end, ar.ARTIKEL_TEXT'
                             +' from'
                             +'   VQ_AUFTRAG_POS pos'
                             +'   inner join V_ARTIKEL ar on (ar.REF=pos.REF_AR)'
                             +'   inner join VQ_ARTIKEL arq on (arq.REF=ar.REF)'
                             +' where'
                             +'   (nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''1'')'
                             +'   and arq.VERSAND_ART is not null'
                             +'   and pos.REF_AUF_KOPF=:ref_auf'
                             +' group by'
                             +'   arq.VERSAND_ART, ar.ARTIKEL_TEXT');
            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            try
              cfgquery.Open;

              idx := 1;

              while not (cfgquery.Eof) do begin
                if not (cfgquery.Fields [0].IsNull) then begin
                  if not firstsvr then jsonstr := jsonstr + ',';
                  firstsvr := false;

                  jsonstr := jsonstr + '{';
                  jsonstr := jsonstr + '"type"'+':'+'"Info'+IntToStr (idx)+'"';
                  jsonstr := jsonstr + ',"code"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.Fields [0].AsString)+'"';
                  jsonstr := jsonstr + ',"value"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.Fields [1].AsString)+'"';
                  jsonstr := jsonstr + '}';

                  Inc (idx);
                end;

                cfgquery.Next;
              end;

              cfgquery.Close;
            except
            end;
          finally
            cfgquery.Free;
          end;

          //Ablageplatz, aktuell nur für GLS
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Die Ablagestelle kann über die Lieferanschrift oder das Sped-Config vorgegeben werden
            cfgquery.SQL.Clear;
            cfgquery.SQL.Add( 'select auf.AUFTRAG_NR, auftxt.VERSAND_HINWEIS, coalesce (adr.DEPOSIT_PLACE,cfg.DEPOSIT_PLACE)'
                             +' from V_SPED_CONFIG cfg, V_AUFTRAG auf, V_AUFTRAG_ADR adr, V_AUFTRAG_TEXTE auftxt'
                             +' where cfg.REF_SPED=:ref_sped and auf.REF=:ref_auf and auftxt.REF_AUF_KOPF=auf.REF and adr.REF=auf.REF_LIEFER_ADR'
                            );
            cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

            try
              cfgquery.Open;

              if not (cfgquery.Fields [2].IsNUll) then begin
                if not firstsvr then jsonstr := jsonstr + ',';
                firstsvr := false;

                jsonstr := jsonstr + '{';
                jsonstr := jsonstr + '"type"'+':'+'"SafePlace"';
                jsonstr := jsonstr + ',"place"'+':'+'"'+ConvertJSONSonderzeichen (cfgquery.Fields [2].AsString)+'"';
                jsonstr := jsonstr + '}';
              end;

              cfgquery.Close;
            except
            end;
          finally
            cfgquery.Free;
          end;


          (*
          if kepemail then begin
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.LockType := ltReadOnly;
              cfgquery.Connection := LVSDatenModul.MainADOConnection;

              cfgquery.SQL.Add ('select bes.ARTIKEL_NR,bes.ARTIKEL_TEXT,bes.COLLI_NAME,ap.MENGE_GESAMT from V_NVE_INHALT bes left outer join VQ_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS) where bes.REF_NVE=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

              cfgquery.Open;

              arnr := cfgquery.FieldByName('ARTIKEL_NR').AsString;

              if not firstsvr then jsonstr := jsonstr + ',';
              firstsvr := false;

              jsonstr := jsonstr + '"type"'+':'+'"internalArticleNr"';
              jsonstr := jsonstr + ',"value"'+':'+'"'+arnr+'"';

              if not (cfgquery.FieldByName('COLLI_NAME').IsNull) then
                csvstr := csvstr + ' / ' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('COLLI_NAME').AsString, ';', ',', [rfReplaceAll]));

              cfgquery.Close;

            finally
              cfgquery.Free;
            end;
          end;
          *)

          //Sperrgut
          if (query.FieldByName('OPT_SPERRGUT').AsString = '1') then begin
            if not (query.FieldByName('OPT_SPERRGUT_AS_NORMAL').AsString = '1') then begin
              if not firstsvr then jsonstr := jsonstr + ',';
              firstsvr := false;

              jsonstr := jsonstr + '{';
              jsonstr := jsonstr + '"type"'+':'+'"Bulk"';
              jsonstr := jsonstr + ',"value"'+':'+'"'+'true'+'"';
              jsonstr := jsonstr + '}';
            end;
          end;

          //Mit Packstation bzw. Paketshop
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:ref');
            cfgquery.Params [0].Value := query.FieldByName('REF_LIEFER_ADR').AsInteger;

            cfgquery.Open;

            if not (cfgquery.FieldByName('PACKING_STATION').IsNull) then begin
              numstr := cfgquery.FieldByName('PACKING_STATION').AsString;

              //Bei Mondial müssen die Nummern mit führenden Null aufgefüllt werden und dürfen nicht länger als 5 Stellen sein.
              if (copy (Versender, 1, 3) = 'MON') then begin
                while (length (numstr) < 5) do numstr := '0' + numstr;
                while (length (numstr) > 5) do Delete (numstr, 1, 1);
              end else if (copy (Versender, 1, 3) = 'GLS') and (liefquery.FieldByName('LAND_ISO').AsString = 'FR') then begin
                while (length (numstr) < 7) do numstr := '0' + numstr;
              end;

              if not firstsvr then jsonstr := jsonstr + ',';
              firstsvr := false;

              jsonstr := jsonstr + '{';
              jsonstr := jsonstr + '"type"'+':'+'"ParcelShop"';
              jsonstr := jsonstr + ',"parcelShopNr"'+':'+'"'+ConvertJSONSonderzeichen (numstr)+'"';
              jsonstr := jsonstr + '}';
            end;

            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

          jsonstr := jsonstr + ']';

          jsonstr := jsonstr + ',"Packages": {';
            jsonstr := jsonstr + '"Package": {';
              jsonstr := jsonstr + '"id"'+':'+'"'+idstr+'"';

              if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
                jsonstr := jsonstr + ',"position"'+':'+'1';
                jsonstr := jsonstr + ',"count"'+':'+'1';
              end else begin
                cfgquery  := TSmartQuery.Create (Nil);

                try
                  cfgquery.ReadOnly := True;
                  cfgquery.Session := Query.Session;

                  //Über SHIPPING_UNITS oder Anzahl der SKUs die Gesamtanzahl der Packstücke bestimme
                  cfgquery.SQL.Add ('select av.SHIPPING_UNITS, sum (MENGE_GESAMT * nvl (ae.ANZAHL_PACKEINHEIT, 1)) from VQ_AUFTRAG_POS pos, VQ_ARTIKEL_EINHEIT ae, VQ_AUFTRAG_VERSAND av'
                                   +' where av.REF_AUF_KOPF=pos.REF_AUF_KOPF and ae.REF=pos.REF_AR_EINHEIT and pos.REF_AUF_KOPF=:ref group by av.SHIPPING_UNITS'
                                   );
                  cfgquery.Params [0].Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                  cfgquery.Open;

                  if not (cfgquery.Fields [0].IsNull) then
                    sumvpe := cfgquery.Fields [0].AsInteger
                  else
                    sumvpe := cfgquery.Fields [1].AsInteger;

                  cfgquery.Close;
                finally
                  cfgquery.Free;
                end;

                jsonstr := jsonstr + ',"position"'+':'+query.FieldByName('PACKAGE_NR').AsString;
                jsonstr := jsonstr + ',"count"'+':'+IntToStr (sumvpe);
              end;

              if (Length (lnstr) > 0) then
                jsonstr := jsonstr + ',"length"'+':'+lnstr;
              if (Length (wistr) > 0) then
                jsonstr := jsonstr + ',"width"'+':'+wistr;
              if (Length (histr) > 0) then
                jsonstr := jsonstr + ',"height"'+':'+histr;

              if (Length (gwstr) > 0) then
                jsonstr := jsonstr + ',"weight"'+':'+StringReplace (gwstr, ',', '.', [rfReplaceAll]);  //Gewicht mit .

              jsonstr := jsonstr + ',"type"'+':"'+ltstr+'"';


              //Artikelinfos
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                cfgquery.SQL.Add ('select'
                                 +' bes.ARTIKEL_NR'
                                 +',GetArtikelTextEx (bes.REF_AR, pa_session_daten.getsprache, ''EXPORT_DESCRIPTION'') as ARTIKEL_TEXT'
                                 +',bes.COLLI_NAME'
                                 +',ap.MENGE_GESAMT'
                                 +' from'
                                 +' V_NVE_INHALT bes'
                                 +' left outer join VQ_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS)'
                                 +' where'
                                 +' bes.REF_NVE=:ref');
                cfgquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

                cfgquery.Open;

                arnr := cfgquery.FieldByName('ARTIKEL_NR').AsString;
                if not (cfgquery.FieldByName('COLLI_NAME').IsNull) then
                  arnr := arnr + ' / ' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('COLLI_NAME').AsString, ';', ',', [rfReplaceAll]));

                //Nur bei Export den Sendungsinhalt bezeichnen
                if not (liefquery.FieldByName('LAND_ISO').IsNull and (liefquery.FieldByName('LAND_ISO').AsString <> 'DE')) then begin
                  if (Length (zolldesc) > 0) then
                    jsonstr := jsonstr + ',"description"'+':'+'"'+ConvertJSONSonderzeichen (zolldesc)+'"'
                  else
                    jsonstr := jsonstr + ',"description"'+':'+'"'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]))+'"';
                end else begin
                  jsonstr := jsonstr + ',"description"'+':'+'"'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]))+'"';
                end;


                jsonstr := jsonstr + ',"ArticleNr"'+':'+'"'+ConvertJSONSonderzeichen (arnr)+'"';
                jsonstr := jsonstr + ',"ArticleDescription"'+':'+'"'+ConvertJSONSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]))+'"';
              finally
                cfgquery.Free;
              end;

            jsonstr := jsonstr + '}';
          jsonstr := jsonstr + '}';


          (* Das ist für V7
          if Assigned (gatequery.FindField ('INCOTERM')) and not (gatequery.FieldByName ('INCOTERM').IsNull) then begin
            jsonstr := jsonstr + ',"incoterm": {';
            jsonstr := jsonstr + '"incotermType": "'+gatequery.FieldByName ('INCOTERM').AsString+'",';
            jsonstr := jsonstr + '"place": null,';
            jsonstr := jsonstr + '"account": null,';
            jsonstr := jsonstr + '"thirdParties": null';
            jsonstr := jsonstr + '}';
          end;
          *)

          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Machen wir jetzt immer
            //Bei Warenpost international müssen auch die Infos für das CN22 bei EU Länder mitgegeben werden
            //if not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString) or ((landstr <> 'DE') and (Versender = 'DHL041')) then begin
              jsonstr := jsonstr + ',"Customs": {';

              jsonstr := jsonstr + '"InvoiceDate"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('VERSAND_DATUM').AsString)+'"';
              jsonstr := jsonstr + ',"InvoiceSubTotal"'+':'+StringReplace (Format('%6.3f', [WarenWert]), ',', '.', [rfReplaceAll]);

              if not (query.FieldByName ('RECHNUNGS_NR').IsNull) then
                jsonstr := jsonstr + ',"InvoiceNo"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('RECHNUNGS_NR').AsString)+'"'
              else
                jsonstr := jsonstr + ',"InvoiceNo"'+':'+'"'+ConvertJSONSonderzeichen (query.FieldByName('AUFTRAG_NR').AsString)+'"';

              if not (query.FieldByName ('CURRENCY').IsNull) then
                jsonstr := jsonstr + ',"InvoiceCurrencyCode"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
              else
                jsonstr := jsonstr + ',"InvoiceCurrencyCode"'+':'+'"'+'EUR'+'"';

              if Assigned (gatequery.FindField ('INCOTERM')) and not (gatequery.FieldByName ('INCOTERM').IsNull) then begin
                jsonstr := jsonstr + ',"TermsOfShipment":"'+gatequery.FieldByName ('INCOTERM').AsString+'"';
              end;

              //Die Zollinfos der Artikel
              cfgquery.SQL.Clear;
              //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
              cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN''),bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                              +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER),nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000'
                              +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                              +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                              +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                              +' from'
                              +'   V_LAGER_NVE_BESTAND bes'
                              +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                              +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                              +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                              +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                              +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                              +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                              +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                              +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                              +'   inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                              +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                              +' where'
                              +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                              +'   and bes.REF_NVE=:ref'
                              );
              cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

              cfgquery.Open;

              idx := 0;

              jsonstr := jsonstr + ',"Lines":[';

              while not (cfgquery.Eof) and (res = 0) do begin
                Inc (idx);

                //Prüfung auf Preis und Zolltarifnummer nur wenn die Ware nach NoEU geht
                if not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString) then begin
                  if (cfgquery.Fields [0].AsString > '0') then begin
                    if (cfgquery.Fields [6].IsNull) then begin
                      res := 56;

                      if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;

                      {$ifdef ResourceText}
                        ErrorText := ErrorText + FormatMessageText (1688, [cfgquery.Fields [1].AsString]);
                      {$else}
                        ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Zolltarifnummer für Artikel %s', [cfgquery.Fields [1].AsString]);
                      {$endif}
                    end;

                    if (cfgquery.Fields [8].IsNull) then begin
                      res := 57;

                      if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                      {$ifdef ResourceText}
                        ErrorText := ErrorText + FormatMessageText (1689, [cfgquery.Fields [1].AsString]);
                      {$else}
                        ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Preis für Artikel %s', [cfgquery.Fields [1].AsString]);
                      {$endif}
                    end;
                  end;
                end;

                if (res = 0) then begin
                  if (idx > 1) then
                    jsonstr := jsonstr + ',{'
                  else
                    jsonstr := jsonstr + '{';

                  jsonstr := jsonstr + '"Nr"'+':'+'"'+IntToStr (idx)+'"';
                  jsonstr := jsonstr + ',"PartNumber"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [1].AsString, 1, 32))+'"';
                  jsonstr := jsonstr + ',"Description1"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [2].AsString, 1, 32))+'"';

                  //Nur ausgeben, wenn die Zollabwicklung aktiv ist
                  //if noneuflag and (cfgquery.Fields [0].AsString > '0') then begin
                  if (cfgquery.Fields [0].AsString > '0') then begin
                     //PriceX, Betrag ist min. 10 Cent
                    if (cfgquery.Fields [8].IsNull or (cfgquery.Fields [8].AsInteger < 10)) then
                      csvstr := Format('%6.3f', [0.1])
                    else begin
                      if (cfgquery.FieldByName ('ANZAHL_VPE').AsInteger > 1) and (cfgquery.FieldByName ('REF_AR').AsInteger <> cfgquery.FieldByName ('POS_REF_AR').AsInteger) then
                        csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger])
                      else
                        csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000]);
                    end;
                    jsonstr := jsonstr + ',"PricePerUnit"'+':'+StringReplace (csvstr, ',', '.', [rfReplaceAll]);

                    if not (query.FieldByName ('CURRENCY').IsNull) then
                      jsonstr := jsonstr + ',"CurrencyCode"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
                    else
                      jsonstr := jsonstr + ',"CurrencyCode"'+':'+'"'+'EUR'+'"';

                    if (cfgquery.Fields [6].IsNull) then
                      taricstr := ''
                    else
                      taricstr := StringReplace (copy (cfgquery.Fields [6].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                    jsonstr := jsonstr + ',"CustomsTariffNumber"'+':'+'"'+ConvertJSONSonderzeichen (taricstr)+'"';
                  end;

                  jsonstr := jsonstr + ',"QuantitySpecified":true,"Quantity"'+':'+copy (cfgquery.Fields [3].AsString, 1, 32);

                  if (copy (Versender, 1, 3) = 'UPS') then
                    jsonstr := jsonstr + ',"QuantityUnit"'+':'+'"PC"'
                  else
                    jsonstr := jsonstr + ',"QuantityUnit"'+':'+'"'+ConvertJSONSonderzeichen (copy (cfgquery.Fields [4].AsString, 1, 32))+'"';

                  //WeightX, Gewicht ist min. 10 Gramm
                  if (cfgquery.Fields [7].IsNull or (cfgquery.Fields [7].AsFloat < 0.01)) then
                    csvstr := '0,01'
                  else
                    csvstr := cfgquery.Fields [7].AsString;

                  jsonstr := jsonstr + ',"WeightSpecified":true,"Weight"'+':'+StringReplace (csvstr, ',', '.', [rfReplaceAll]);

                  //OriginCountryX
                  if (cfgquery.Fields [5].IsNull) then
                    csvstr := 'DE'
                  else
                    csvstr := StringReplace (copy (cfgquery.Fields [5].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                  jsonstr := jsonstr + ',"OriginCountry"'+':'+'"'+csvstr+'"';

                  jsonstr := jsonstr + '}';
                end;

                cfgquery.Next;
              end;

              jsonstr := jsonstr + ']';

              cfgquery.Close;

              jsonstr := jsonstr + '}';
            //end;
          finally
            cfgquery.Free;
          end;

          if (Length (gwstr) > 0) then
            jsonstr := jsonstr + ',"ShipmentWeight"'+':'+StringReplace (gwstr, ',', '.', [rfReplaceAll]);  //Gewicht mit .

          if (WarenWert > 0) then begin
            jsonstr := jsonstr + ',"ShipmentValue"'+':'+StringReplace (Format('%6.3f', [WarenWert]), ',', '.', [rfReplaceAll]);  //Gewicht mit .

            if not (query.FieldByName ('CURRENCY').IsNull) then
              jsonstr := jsonstr + ',"ShipmentValueCurrency"'+':'+'"'+query.FieldByName ('CURRENCY').AsString+'"'
            else
              jsonstr := jsonstr + ',"ShipmentValueCurrency"'+':'+'"'+'EUR'+'"';
          end;

          jsonstr := jsonstr + '}';
        end;

        //Wenn ein Fehler erkannt wurde, wird kein Label gedruckt
        if (res = 0) then begin
          ForceDirectories(DatenPath + RESTDumpDir+'SendIT\'+FormatDateTime ('yyyymmdd', Now));

          StrToFile (DatenPath + RESTDumpDir+'SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\'+Versender+'_'+nvestr+'.json', jsonstr);

          sdata := TMemoryStream.Create;

          try
            sdata.Clear;
            if SendRequest(urlstr, // Host,
                            portint, //Port
                            apistr, // Service
                            'POST', //Methode
                            '', // Proxy,
                            '', '', // User , PW
                            '', //Action
                            'application/json', //ContentType
                            [], //AddHeader
                            UTF8Encode (jsonstr),         // RequestData
                            resp,
                            sdata, //ResponseStream
                            errcode, // Fehlercode
                            errtext) // Fehlertext
                          then
            begin
              StrToFile (DatenPath + RESTDumpDir+'SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\'+Versender+'_resp_'+nvestr+'.json', resp);

              sdata.Position := 0;
              sdata.SaveToFile(DatenPath + RESTDumpDir+'SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\'+Versender+'_tracking_'+nvestr+'.json');

              js := Nil;

              try
                if (copy (uppercase (resp), 1, 12) <> 'HTTP/1.1 200') then begin
                  res := 55;

                  fs := Nil;

                  {$ifdef UNICODE}
                    SetLength(utfstr, sdata.Size);
                    sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                    datastr := StringUtils.StringToUTF (utfstr);

                    js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                  {$else}
                    sdata.Position := 0;
                    js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                  {$endif}

                  if not Assigned (js) then begin
                    {$ifdef UNICODE}
                      ErrorText := datastr;
                    {$else}
                      ErrorText := MemoryStreamToString (sdata);
                    {$endif}
                  end else begin
                    us := js.Field['Message'];
                    if Assigned (us) then
                      ErrorText := us.Value
                    else
                      ErrorText := MemoryStreamToString (sdata)
                  end;
                end else begin
                  fs := Nil;

                  {$ifdef UNICODE}
                    SetLength(utfstr, sdata.Size);
                    sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                    datastr := StringUtils.StringToUTF (utfstr);

                    js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                  {$else}
                    sdata.Position := 0;
                    js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                  {$endif}

                  if Assigned (js) then
                    fs := js.Field['PackageResponse'];

                  if not Assigned (fs) then begin
                    res := 11;
                    ErrorText := 'API error message';
                  end else begin
                    us := fs.Field['TrackingID'];
                    if not Assigned (us) then begin
                      res := 15;
                      ErrorText := 'tracking_number code error';
                    end else begin
                      SendungsNr := us.Value;
                    end;

                    us := fs.Field['PackageBarcode'];
                    if Assigned (us) then begin
                      Barcode := us.Value;
                    end;

                    us := fs.Field['URL'];
                    if Assigned (us) then begin
                      TrackUrl := us.Value;
                    end;

                    us := fs.Field['PrnString'];
                    if Assigned (us) then begin
                      if not (VarIsNull (us.Value)) then begin
                        lblstr := us.Value;

                        if (Length (lblstr) > 0) then begin
                          LabelImage.WriteBuffer (Pointer(lblstr)^, Length (lblstr));
                          LabelFormat := 'zpl';

                          if not (DirectoryExists (DatenPath + LabelDumpDir+'SendIT')) then
                            ForceDirectories(DatenPath + LabelDumpDir + 'SendIT');

                          LabelImage.Position := 0;

                          try
                            LabelImage.SaveToFile(DatenPath + LabelDumpDir + 'SendIT\' + Versender+'_'+SendungsNr + '.' + LabelFormat);
                          except
                          end;
                        end;
                      end;
                    end;
                  end;
                end;
              finally
                if Assigned (js) then
                  js.Free;
              end;
            end else begin
              res := errcode;
              ErrorText := errtext;

              StrToFile (DatenPath + RESTDumpDir + 'SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\'+Versender+'_resp_'+nvestr+'.json', resp);
            end;
          finally
            sdata.Free;
          end;
        end;
      end else if Assigned (SendITMapping) then begin
        for s := 0 to High (csvwerte) do
          csvwerte [s] := '';

        try
          SetCSVValue ('Command', Art);
          SetCSVValue ('ShippingType', Versender);
          SetCSVValue ('StationID', prtname);

          if (Pos ('Return', Versender) > 0) then
            csvstr := '0'
          else if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then
            csvstr := '1'
          else
            csvstr := '0';

          if (Pos ('TypeDelivery', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            SetCSVValue ('TypeDelivery', 'true');

          SetCSVValue ('COD', csvstr);
          SetCSVValue ('ClientID', Absender);

          if (Pos ('INV=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('INV=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else if (Pos ('INV=AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUFTRAG_NR').AsString
          else if (Pos ('INV=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_BESTELL_NR').AsString
          else if (Pos ('INV=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
          else if (Pos ('INV=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('RECHNUNGS_NR').IsNull then
            csvstr := query.FieldByName('RECHNUNGS_NR').AsString
          else if not query.FieldByName('RECHNUNGS_NR').IsNull then
            csvstr := query.FieldByName('RECHNUNGS_NR').AsString
          else
            csvstr := query.FieldByName('AUFTRAG_NR').AsString;

          SetCSVValue ('InvoiceNr', csvstr);

          if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else if (Pos ('REF=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_BESTELL_NR').IsNull then
            csvstr := query.FieldByName('KD_BESTELL_NR').AsString
          else if (Pos ('REF=KD_AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_AUFTRAG_NR').IsNull then
            csvstr := query.FieldByName('KD_AUFTRAG_NR').AsString
          else if (Pos ('REF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
          else if (Pos ('REF=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('RECHNUNGS_NR').AsString
          else csvstr := query.FieldByName('AUFTRAG_NR').AsString;

          SetCSVValue ('ReferenceNr', csvstr);

          if (Pos ('REF2=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF2=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else if (Pos ('REF2=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_BESTELL_NR').IsNull then
            csvstr := query.FieldByName('KD_BESTELL_NR').AsString
          else if (Pos ('REF2=KD_AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) and not query.FieldByName('KD_AUFTRAG_NR').IsNull then
            csvstr := query.FieldByName('KD_AUFTRAG_NR').AsString
          else if (Pos ('REF2=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
          else if (Pos ('REF2=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else csvstr := '';

          SetCSVValue ('ReferenceNr2', csvstr);

          SetCSVValue ('DeliveryNoteNr', query.FieldByName('LIEFERSCHEIN_NR').AsString);
          SetCSVValue ('CustomerAccountNr', query.FieldByName('KUNDEN_NR').AsString);

          if not (query.FieldByName('KD_AUFTRAG_NR').IsNull) then
            SetCSVValue ('CustomerOrderNr', query.FieldByName('KD_AUFTRAG_NR').AsString)
          else
            SetCSVValue ('CustomerOrderNr', query.FieldByName('KD_BESTELL_NR').AsString);

          //VAT oder ander Einfuhrsteuernummern
          if Assigned (gatequery.FindField ('DEFAULT_CUSTOM_NR')) and not gatequery.FieldByName ('DEFAULT_CUSTOM_NR').IsNull then
            SetCSVValue ('CustomsReferenceNrSender', gatequery.FieldByName ('DEFAULT_CUSTOM_NR').AsString)
          else if Assigned (liefquery.FindField ('UST_ID')) and not liefquery.FieldByName ('UST_ID').IsNull then
            SetCSVValue ('CustomsReferenceNrSender', liefquery.FieldByName ('UST_ID').AsString);

          //Bei OPT_MULTI_SHIPMEND=0 ist jedes Packstück eine eigen Sendungen
          if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
            SetCSVValue ('ShipmentID', shipid);
            SetCSVValue ('ShipmentPos', '1');
            SetCSVValue ('ShipmentCnt', '1');
          end else begin
            SetCSVValue ('ShipmentID', shipid);

            //Bei OPT_MULTI_SHIPMEND > 0 pro Auftrag eine Sendungen mit Anzahl Packstücken
            if (Pos ('Return', Versender) > 0) then begin
              SetCSVValue ('ShipmentPos', '1');
              SetCSVValue ('ShipmentCnt', '1');
            end else if (true=true) then begin
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                //Über SHIPPING_UNITS oder Anzahl der SKUs die Gesamtanzahl der Packstücke bestimmen
                cfgquery.SQL.Add ('select av.SHIPPING_UNITS, sum (MENGE_GESAMT * nvl (ae.ANZAHL_PACKEINHEIT, 1)) from VQ_AUFTRAG_POS pos, VQ_ARTIKEL_EINHEIT ae, VQ_AUFTRAG_VERSAND av'
                                 +' where av.REF_AUF_KOPF=pos.REF_AUF_KOPF and ae.REF=pos.REF_AR_EINHEIT and pos.REF_AUF_KOPF=:ref group by av.SHIPPING_UNITS'
                                 );
                cfgquery.Params [0].Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if not (cfgquery.Fields [0].IsNull) then
                  sumvpe := cfgquery.Fields [0].AsInteger
                else
                  sumvpe := cfgquery.Fields [1].AsInteger;

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;

              SetCSVValue ('ShipmentPos', query.FieldByName('PACKAGE_NR').AsString);
              SetCSVValue ('ShipmentCnt', IntToStr (sumvpe));
            end else begin
              SetCSVValue ('ShipmentPos', query.FieldByName('PACKAGE_NR').AsString);
              SetCSVValue ('ShipmentCnt', query.FieldByName('PACKAGE_COUNT').AsString);
            end;
          end;

          SetCSVValue ('PackageID', idstr);

          if ((copy (Versender, 1, 7) = 'Dachser') and not (query.FieldByName('NVE_NR').IsNull)) then
            SetCSVValue ('TrackingID', query.FieldByName('NVE_NR').AsString);

          //Bei DHL ES muss das Gewicht auf 5 kg gesetzt werden, sonst wird eine Bulky-Sendung erzeugt
          if (copy (Versender, 1, 8) = 'DHLSpain') and (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 5.1) then begin
            csvstr := '5.1';
          end else if (query.FieldByName('BRUTTO_GEWICHT').IsNull) then
            csvstr := '2'
          else if (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 0.1) then
            csvstr := '0.100'
          else
            csvstr := Format ('%0.3f', [query.FieldByName('BRUTTO_GEWICHT').AsFloat]);

          SetCSVValue ('PackageWeight', csvstr);

          if (Pos ('Return', Versender) > 0) then
            SetCSVValue ('ShipmentValueCurrency', 'EUR')
          else begin
            SetCSVValue ('ShipmentValue', Format ('%0.3f', [WarenWert]));

            if not (query.FieldByName ('CURRENCY').IsNull) then
              SetCSVValue ('ShipmentValueCurrency', query.FieldByName ('CURRENCY').AsString)
            else
              SetCSVValue ('ShipmentValueCurrency', 'EUR');

            //Nachnahme nur wenn auch ein Wert angegeben ist
            if (WarenWert <= 0) or (query.FieldByName('OPT_NACHNAHME').AsString = '0') then
              csvstr := csvstr + ';'
            else begin
              if ((lowercase (copy (query.FieldByName('SUB_MANDANT').AsString, 1, 5)) = 'leroi') and not query.FieldByName('KUNDEN_NR').IsNull) then
                textstr := ConvertSonderzeichen ('Kundennr: '+query.FieldByName('KUNDEN_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString)
              else if not (query.FieldByName('RECHNUNGS_NR').IsNull) then
                textstr := ConvertSonderzeichen ('Rechnungsnr: '+query.FieldByName('RECHNUNGS_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString)
              else
                textstr := ConvertSonderzeichen ('Auftragsnr: '+query.FieldByName('AUFTRAG_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString);

              SetCSVValue ('CODPurpose', textstr);
            end;
          end;

          if (copy (Versender, 1, 3) = 'PFR') then begin
            //Bei Colissimo muss ShipToAdresse1 der Kundennamen udn ShipToAdresse2 die Firma sein
            if (liefquery.FieldByName('COMPANY').IsNull) then begin
              //ShipToAdresse1
              SetCSVValue ('ShipToAddress1', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll])));

              //ShipToAdresse2
              if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                SetCSVValue ('ShipToAddress2', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll])))
              else if (Length (liefquery.FieldByName('STRASSE_2').AsString) > 4) then
               //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
                SetCSVValue ('ShipToAddress2', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll])))
            end else begin
              //ShipToAdresse1, bei uns in dem Falle dann NAME2
              SetCSVValue ('ShipToAddress1', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll])));

              //ShipToAdresse2
              SetCSVValue ('ShipToAddress2', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll])));
            end;
          end else begin
            //ShipToAdresse1
            SetCSVValue ('ShipToAddress1', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll])));

            if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              SetCSVValue ('ShipToAddress2', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll])))
            else if (Length (streetaddstr) > 0) then
             //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
              SetCSVValue ('ShipToAddress2', ConvertSonderzeichen (StringReplace (streetaddstr, ';', ',', [rfReplaceAll])))
            else if (copy (Versender, 1, 3) = 'HVS') then
              //Hermes benötig hier immer einen Text
              SetCSVValue ('ShipToAddress2', '-');
          end;

          //Bei Hermes gib es keine dritte Adresszeile
          if (copy (Versender, 1, 3) = 'HVS') then
            csvstr := csvstr + ';'
          else begin
            if (Length (liefquery.FieldByName('NAMEZUSATZ').AsString) > 0) then
              SetCSVValue ('ShipToAddress3', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAMEZUSATZ').AsString, ';', ',', [rfReplaceAll])))
            else if (Length (streetaddstr) > 0) and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              SetCSVValue ('ShipToAddress3', ConvertSonderzeichen (StringReplace (streetaddstr, ';', ',', [rfReplaceAll])));
          end;

          SetCSVValue ('ShipToFirstName', ConvertSonderzeichen (StringReplace (vornamestr, ';', ',', [rfReplaceAll])));
          SetCSVValue ('ShipToLastName', ConvertSonderzeichen (StringReplace (nachnamestr, ';', ',', [rfReplaceAll])));

          //Nur wenn es auch das CSV-Feld ShipToHouseNr gibt, darf auch die Hausnummer getrennt von der Strasse ausggben werden
          if CheckCSVValue ('ShipToHouseNr') then begin
            SetCSVValue ('ShipToStreet', ConvertSonderzeichen (streetstr));
            SetCSVValue ('ShipToHouseNr', ConvertSonderzeichen (nrstr));
          end else begin
            SetCSVValue ('ShipToStreet', ConvertSonderzeichen (liefquery.FieldByName('STRASSE').AsString));
          end;

          SetCSVValue ('ShipToPostalCode', StringReplace (liefquery.FieldByName('PLZ').AsString, ';', ',', [rfReplaceAll]));
          SetCSVValue ('ShipToCity', ConvertSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll])));

          //Für UPS wird der State-Code für US benötigt.
          if Assigned (liefquery.FindField ('STATE')) and not (liefquery.FieldByName('STATE').IsNull) then
            SetCSVValue ('ShipToStateOrProvinceCode', liefquery.FieldByName('STATE').AsString);

          SetCSVValue ('ShipToCountry', isostr);
          SetCSVValue ('ShipToPhone', telstr);

          if (LabelType <> 'Return') then begin
            //Versandbenachrichtigungen per Mail
            if (Pos ('MAIL', notifystr) > 0 ) then begin
              if Assigned (gatequery.FindField ('NOTIFICATION_BY_MAIL')) then begin
                if (gatequery.FieldByName('NOTIFICATION_BY_MAIL').AsString = '1') then begin
                  SetCSVValue ('ShipToMail', mailstr);

                  SetCSVValue ('Notification', 'true');
                  SetCSVValue ('NotificationEMail', mailnotystr);
                end else if (gatequery.FieldByName('NOTIFICATION_BY_MAIL').AsString = '2') then begin
                  SetCSVValue ('ShipToMail', mailstr);

                  SetCSVValue ('Notification', 'true');
                  SetCSVValue ('NotificationEMail', mailnotystr);
                  SetCSVValue ('NotificationTypeDelivery', '1');
                end else begin
                  SetCSVValue ('ShipToMail', mailstr);

                  SetCSVValue ('Notification', 'true');
                  SetCSVValue ('NotificationEMail', mailnotystr);

                  if (copy (Versender, 1, 6) = 'SCH002') or (copy (Versender, 1, 6) = 'SCH006') then begin
                    if (Length (telstr) > 0) then
                      SetCSVValue ('NotificationPhone', telstr)
                    else
                      SetCSVValue ('NotificationPhone', phonenotystr);

                    SetCSVValue ('NotificationTypeDelivery', '1');
                  end;
                end;
              end else begin
                SetCSVValue ('ShipToMail', mailstr);
              end;
            end;

            //Avisierung per Telefon
            if (Pos ('PHONE', notifystr) > 0 ) or (Pos ('TELEFON', notifystr) > 0 ) then begin
              SetCSVValue ('Notification', 'true');
              SetCSVValue ('NotificationPhone', phonenotystr);
            end;

            //Ein Platformavisierung ist notwendig (DB-Schenker)
            if (Pos ('PLATFORM', notifystr) > 0 ) then begin
              SetCSVValue ('Advice_1', ConvertSonderzeichen (StringReplace (query.FieldByName ('AVIS_HINWEIS').AsString, ';', ',', [rfReplaceAll])));
            end;
          end;

          csvstr := '';

          if (copy (Versender, 1, 8) = 'DHLSpain') then
          else begin
            if not (liefquery.FieldByName('ANSPRECHPARTNER').IsNull) then begin
              if (copy (Versender, 1, 3) = 'SCH') then
                csvstr := ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else
                csvstr := ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 35), ';', ',', [rfReplaceAll]))
            end else if not (adrquery.FieldByName('ANSPRECHPARTNER').IsNull) then begin
              if (copy (Versender, 1, 3) = 'SCH') then
                csvstr := ConvertSonderzeichen (StringReplace (copy (adrquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else
                csvstr := ConvertSonderzeichen (StringReplace (adrquery.FieldByName('ANSPRECHPARTNER').AsString, ';', ',', [rfReplaceAll]))
            end else if not (liefquery.FieldByName('LAND_ISO').IsNull or (liefquery.FieldByName('LAND_ISO').AsString = 'DE')) then
              //Nur bei Export den Empfänger als Ansprechpartner angeben
              csvstr := ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]))
            else if ((copy (Versender, 1, 3) = 'SCH') or (copy (Versender, 1, 3) = 'SCH')) then
              //Ansprechpartner ist bei Schenker Pflicht
              csvstr := ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]))
          end;

          SetCSVValue ('ShipToContactName', csvstr);
          SetCSVValue ('ShippingLocationID', VersandStelle);

          if not (Assigned (spedquery.FindField ('OPT_DIM_SEND'))) or (spedquery.FieldByName ('OPT_DIM_SEND').AsString = '1') then begin
            SetCSVValue ('PackageLength', lnstr);
            SetCSVValue ('PackageWidth', wistr);
            SetCSVValue ('PackageHeight', histr);
          end;

          csvstr := '0';

          if (query.FieldByName('OPT_SPERRGUT').AsString = '1') then begin
            if not (query.FieldByName('OPT_SPERRGUT_AS_NORMAL').AsString = '1') then
              csvstr := '1';
          end;

          SetCSVValue ('Bulk', csvstr);

          SetCSVValue ('PackageType', ltstr);

          //Nur bei Export den Sendungsinhalt bezeichnen
          if not (liefquery.FieldByName('LAND_ISO').IsNull and (liefquery.FieldByName('LAND_ISO').AsString <> 'DE')) then begin
            if (Length (zolldesc) > 0) then begin
              SetCSVValue ('PackageDescription', zolldesc);
              SetCSVValue ('CustomsShortDescription', zolldesc);
            end else begin
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                cfgquery.SQL.Clear;
                cfgquery.SQL.Add ('select'
                                 +' bes.ARTIKEL_NR'
                                 +',GetArtikelTextEx (bes.REF_AR, pa_session_daten.getsprache, ''EXPORT_DESCRIPTION'') as ARTIKEL_TEXT'
                                 +',bes.COLLI_NAME'
                                 +',ap.MENGE_GESAMT'
                                 +' from'
                                 +' V_NVE_INHALT bes'
                                 +' left outer join VQ_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS)'
                                 +' where'
                                 +' bes.REF_NVE=:ref');
                cfgquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

                cfgquery.Open;

                csvstr := StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]);

                cfgquery.Close;

                SetCSVValue ('PackageDescription', csvstr);
                SetCSVValue ('CustomsShortDescription', csvstr);
              finally
                cfgquery.Free;
              end;
            end;

            if Assigned (liefquery.FindField ('EORI')) then
              SetCSVValue ('EoriReceiver', liefquery.FieldByName('EORI').AsString);
          end;

          if (Length (gwstr) > 0) then
            SetCSVValue ('ShipmentWeight', gwstr);

          //SetCSVValue ('UserID', LVSDatenModul.AktUserNumID);

          if (copy (Versender, 1, 6) = 'GeisCZ') then begin
            SetCSVValue ('Pickup', 'false');
          end;

          //Nur für DB-Schenker Fixtermin
          if (copy (Versender, 1, 6) = 'SCH006') then begin
            SetCSVValue ('ExpressDay', query.FieldByName ('LIEFER_DATUM').AsString);
          end;

          //Absender Adresse übergeben
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            //Bei Dropship wird die Traderadresse angezeigt
            cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                               +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                               +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                               +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                               +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                               +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                              +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                              +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                              +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                              +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                              +' where auf.REF=:ref_auf'
                              );

            cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

            cfgquery.Open;

            if (Pos ('SENDIT_ABSENDER', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then begin
              ;
            end else if not (AktSendITIFCAbsender) or (Pos ('Return', Versender) > 0) then begin
              ;
            end else begin
              if not (cfgquery.FieldByName('REF').IsNull) then begin
                SetCSVValue ('ShipFromAddress1', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('NAME1').AsString, ';', ',', [rfReplaceAll])));
                SetCSVValue ('ShipFromAddress2', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('NAMEZUSATZ').AsString, ';', ',', [rfReplaceAll])));
                SetCSVValue ('ShipFromStreet', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('STRASSE').AsString, ';', ',', [rfReplaceAll])));
                SetCSVValue ('ShipFromPostalCode', cfgquery.FieldByName ('PLZ').AsString);
                SetCSVValue ('ShipFromCity', ConvertSonderzeichen (cfgquery.FieldByName ('ORT').AsString));

                //Das Land
                 SetCSVValue ('ShipFromCountry', cfgquery.FieldByName ('LAND').AsString);

                //Das Telefon
                SetCSVValue ('ShipFromPhone', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('TELEFON').AsString, ';', ',', [rfReplaceAll])));
              end else begin
                //Ansonsten die vom Lager
                cfgquery.Close;

                cfgquery.SQL.Clear;

                cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME),ml.ABSENDER_ZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE),nvl (ml.ABSENDER_PLZ,loc.PLZ),nvl (ml.ABSENDER_ORT,loc.ORT),nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                  +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                  +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                  +' where m.REF=:ref_mand'
                                  );

                cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

                if query.FieldByName('REF_SUB_MAND').IsNull then
                  cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                cfgquery.Open;

                SetCSVValue ('ShipFromAddress1', StringReplace (cfgquery.Fields [0].AsString, ';', ',', [rfReplaceAll]));
                SetCSVValue ('ShipFromAddress2', StringReplace (cfgquery.Fields [1].AsString, ';', ',', [rfReplaceAll]));
                SetCSVValue ('ShipFromStreet', StringReplace (cfgquery.Fields [2].AsString, ';', ',', [rfReplaceAll]));
                SetCSVValue ('ShipFromPostalCode', StringReplace (cfgquery.Fields [3].AsString, ';', ',', [rfReplaceAll]));
                SetCSVValue ('ShipFromCity', StringReplace (cfgquery.Fields [4].AsString, ';', ',', [rfReplaceAll]));

                //Das Land
                SetCSVValue ('ShipFromCountry', cfgquery.Fields [5].AsString);

                //Das Telefon
                SetCSVValue ('ShipFromPhone', cfgquery.Fields [6].AsString);
              end;
            end;
            cfgquery.Close;
          finally
            cfgquery.Free;
          end;

          //Zusätzliche Versandoptioen über Textartikel DHL - 2MH
          if CheckCSVValue ('Info[1]Code') then begin
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              cfgquery.SQL.Add ('select'
                               +'   case when arq.VERSAND_ART=''~'' then null else arq.VERSAND_ART end, ar.ARTIKEL_TEXT'
                               +' from'
                               +'   VQ_AUFTRAG_POS pos'
                               +'   inner join V_ARTIKEL ar on (ar.REF=pos.REF_AR)'
                               +'   inner join VQ_ARTIKEL arq on (arq.REF=ar.REF)'
                               +' where'
                               +'   (nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''1'')'
                               +'   and arq.VERSAND_ART is not null'
                               +'   and pos.REF_AUF_KOPF=:ref_auf'
                               +' group by'
                               +'   arq.VERSAND_ART, ar.ARTIKEL_TEXT');
              cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

              try
                cfgquery.Open;

                idx := 1;

                while not (cfgquery.Eof) do begin
                  if not (cfgquery.Fields [0].IsNull) then begin
                    SetCSVValue ('Info['+IntToStr (idx)+']Code', cfgquery.Fields [0].AsString);
                    SetCSVValue ('Info['+IntToStr (idx)+']Value', cfgquery.Fields [1].AsString);

                    Inc (idx);
                  end;

                  cfgquery.Next;
                end;

                cfgquery.Close;
              except
              end;
            finally
              cfgquery.Free;
            end;
          end;

          (* Wird nicht mehr unterstützt
          if (AktSendITIFCImpVersion = 6) then begin
            //Vorausverfügung
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.LockType := ltReadOnly;
              cfgquery.Connection := LVSDatenModul.MainADOConnection;

              cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

              cfgquery.Open;

              if (cfgquery.FieldByName('OPT_DISPOSAL').IsNull) then begin
                csvstr := csvstr + ';';
                csvstr := csvstr + ';';
                csvstr := csvstr + ';';
              end else begin
                optstr := cfgquery.FieldByName('OPT_DISPOSAL').AsString;

                if ((Length (optstr) > 0) and (copy (optstr, 1, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';

                if ((Length (optstr) > 0) and (copy (optstr, 2, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';

                if ((Length (optstr) > 0) and (copy (optstr, 3, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';
              end;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;
          *)

          //Auftragsnummer für DB-Schenker usw.
          if (Pos ('AUF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('AUF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else if (Pos ('AUF=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_BESTELL_NR').AsString
          else if (Pos ('AUF=KD_AUFTRAG_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_AUFTRAG_NR').AsString
          else if (Pos ('AUF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
          else if (Pos ('AUF=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('RECHNUNGS_NR').AsString
          else if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('AUF_REFERENZ').AsString
          else if (Pos ('REF=KD_BESTELL_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('KD_BESTELL_NR').AsString
          else if (Pos ('REF=LIEFERSCHEIN_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('LIEFERSCHEIN_NR').AsString
          else if (Pos ('REF=RECHNUNGS_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := query.FieldByName('RECHNUNGS_NR').AsString
          else csvstr := query.FieldByName('AUFTRAG_NR').AsString;

          SetCSVValue ('OrderNr', csvstr);

          //Packetshop-Angaben für Modial (Lillydoo)
          if (Pos ('Return', Versender) = 0) and CheckCSVValue ('ParcelShop') then begin
            //Mit Packstation bzw. Paketshop
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              cfgquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_LIEFER_ADR').AsInteger;

              cfgquery.Open;

              if not (cfgquery.FieldByName('PACKING_STATION').IsNull) then begin
                numstr := cfgquery.FieldByName('PACKING_STATION').AsString;

                if (copy (Versender, 1, 3) = 'MON') then begin
                  while (length (numstr) < 5) do numstr := '0' + numstr;
                  while (length (numstr) > 5) do Delete (numstr, 1, 1);
                end;

                SetCSVValue ('ParcelShop', 'true');
                SetCSVValue ('ParcelShopNr', numstr);
              end;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;

          //Lieferscheinnummer
          SetCSVValue ('DeliveryNoteNr', query.FieldByName('LIEFERSCHEIN_NR').AsString);       //<map outside="xx" inside="DeliveryNoteNr" />

          //DHL2Mann
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            if (CheckCSVValue ('ArticleNr') or CheckCSVValue ('FreightTerms')) then begin
              cfgquery.SQL.Add ('select'
                               +' bes.ARTIKEL_NR'
                               +',GetArtikelTextEx (bes.REF_AR, pa_session_daten.getsprache, ''EXPORT_DESCRIPTION'') as ARTIKEL_TEXT'
                               +',bes.COLLI_NAME'
                               +',ap.MENGE_GESAMT'
                               +' from'
                               +' V_NVE_INHALT bes'
                               +' left outer join VQ_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS)'
                               +' where'
                               +' bes.REF_NVE=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

              cfgquery.Open;

              arnr := cfgquery.FieldByName('ARTIKEL_NR').AsString;

              SetCSVValue ('ArticleNr', arnr);

              if (cfgquery.FieldByName('MENGE_GESAMT').AsInteger > 0) then                                                       //<map outside="xx" inside="ArticleQuantity", neu ab 27.02.2020 />
                SetCSVValue ('ArticleQuantity', cfgquery.FieldByName('MENGE_GESAMT').AsString);

              csvstr := ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]));    //<map outside="xx" inside="ArticleDescription" />

              if not (cfgquery.FieldByName('COLLI_NAME').IsNull) then
                csvstr := csvstr + ' / ' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('COLLI_NAME').AsString, ';', ',', [rfReplaceAll]));

              SetCSVValue ('ArticleDescription', csvstr);

              cfgquery.Close;


              //Verstandart über den Artikel bestimmen
              if (Length (arnr) > 0) and CheckCSVValue ('FreightTerms') then begin
                versart := '';

                cfgquery.SQL.Clear;

                if (query.FieldByName('REF_SUB_MAND').IsNull) then begin
                  cfgquery.SQL.Add ( 'select nvl (con.CONVERT_VALUE, nvl (case when ae.VERSAND_ART=''~'' then null else ae.VERSAND_ART end, case when ar.VERSAND_ART=''~'' then null else ar.VERSAND_ART end))'
                                    +' from VQ_ARTIKEL ar'
                                    +' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF_AR=ar.REF)'
                                    +' left outer join VQ_CONVERT_VALUES con on (con.CENVERT_TYP=''FreightTerms'' and con.CENVERT_FROM=''SendIT'' and con.CONVERT_TO=:kenn and con.VALUE=ar.VERSAND_ART)'
                                    +' where ar.REF_MAND=:ref_mand and ar.REF_SUB_MAND is null and ar.ARTIKEL_NR=:ar_nr'
                                    +' order by case when ae.OPT_MASTER=''1'' then 0 else 9 end asc'
                                   );
                  cfgquery.Params.ParamByName ('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger;
                end else begin
                  cfgquery.SQL.Add ( 'select nvl (con.CONVERT_VALUE, nvl (case when ae.VERSAND_ART=''~'' then null else ae.VERSAND_ART end, case when ar.VERSAND_ART=''~'' then null else ar.VERSAND_ART end))'
                                    +' from VQ_ARTIKEL ar'
                                    +' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF_AR=ar.REF)'
                                    +' left outer join VQ_CONVERT_VALUES con on (con.CENVERT_TYP=''FreightTerms'' and con.CENVERT_FROM=''SendIT'' and con.CONVERT_TO=:kenn and con.VALUE=ar.VERSAND_ART)'
                                    +' where ar.REF_SUB_MAND=:ref_mand and ar.ARTIKEL_NR=:ar_nr'
                                    +' order by case when ae.OPT_MASTER=''1'' then 0 else 9 end asc'
                                   );
                  cfgquery.Params.ParamByName ('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;
                end;
                cfgquery.Params.ParamByName ('ar_nr').Value := arnr;
                cfgquery.Params.ParamByName ('kenn').Value := query.FieldByName('DFUE_KENNZEICHEN').AsString;

                cfgquery.Open;

                versart := cfgquery.Fields[0].AsString;

                cfgquery.Close;

                SetCSVValue ('FreightTerms', versart);                             //<map outside="xx" inside="FreightTerms" />
              end;
            end;

            //Gefahrgut über die UN-Nummer der enthalten Artikels
            if (CheckCSVValue ('HazardUNNr1')) then begin
              if not (Assigned (spedquery.FindField ('OPT_HAZARD_INFO'))) or (spedquery.FieldByName ('OPT_HAZARD_INFO').AsString <> '0') then begin
                cfgquery.SQL.Clear;
                cfgquery.SQL.Add ('select ag.* from V_ARTIKEL_GEFAHRSTOFFE ag, V_ARTIKEL_REL_GEFAHRSTOFFE agr where agr.STATUS=''AKT'' and ag.REF=agr.REF_GEFAHRSTOFFE and agr.REF_AR in (select REF_AR from V_LAGER_NVE_BESTAND where REF_NVE=:ref_nve)');
                cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

                cfgquery.Open;

                if (copy (Versender, 1, 3) = 'DHL') then begin
                  //Bei DHL das Gefahrgut-Kenzeichen nicht an SendIT melden
                end else begin
                  if not (cfgquery.Fields [0].IsNull) then begin
                    SetCSVValue ('HazardUNNr1', cfgquery.FieldByName ('UN_NR').AsString);
                    SetCSVValue ('HazardUNNr[1]', cfgquery.FieldByName ('UN_NR').AsString);
                    SetCSVValue ('HazardAmount1', '1');
                    SetCSVValue ('HazardAmount[1]', '1');
                    SetCSVValue ('HazardClass1', cfgquery.FieldByName ('UN_CLASS').AsString);  //Benötigt UPS
                    SetCSVValue ('HazardClass[1]', cfgquery.FieldByName ('UN_CLASS').AsString);  //Benötigt UPS
                    SetCSVValue ('HazardName1', cfgquery.FieldByName ('WARNING_TEXT').AsString);   //Benötigt UPS
                    SetCSVValue ('HazardName[1]', cfgquery.FieldByName ('WARNING_TEXT').AsString);   //Benötigt UPS

                    if Assigned (cfgquery.FindField ('PACKING_TYPE')) then begin
                      SetCSVValue ('HazardPackingType1', '4G');   //Benötigt UPS
                      SetCSVValue ('HazardPackingType[1]', '4G');   //Benötigt UPS
                    end else begin
                      SetCSVValue ('HazardPackingType1', ltstr);   //Benötigt UPS
                      SetCSVValue ('HazardPackingType[1]', ltstr);   //Benötigt UPS
                    end;
                  end else begin
                    cfgquery.Close;

                    cfgquery.SQL.Clear;
                    cfgquery.SQL.Add ('select * from V_LT_TYPEN where REF=:ref_lt');
                    cfgquery.Params [0].Value := query.FieldByName('REF_LT_TYP').AsInteger;

                    cfgquery.Open;

                    if Assigned (cfgquery.FindField ('UN_NR')) then begin
                      if not (cfgquery.FieldByName ('UN_NR').IsNull) then begin
                        SetCSVValue ('HazardUNNr1', cfgquery.FieldByName ('UN_NR').AsString);
                        SetCSVValue ('HazardUNNr[1]', cfgquery.FieldByName ('UN_NR').AsString);

                        if (cfgquery.FindField ('TARA_GEWICHT').AsInteger > 200) then begin
                          SetCSVValue ('HazardAmount1', Format ('%0.1f', [cfgquery.FindField ('TARA_GEWICHT').AsInteger / 1000]));
                          SetCSVValue ('HazardAmount[1]', Format ('%0.1f', [cfgquery.FindField ('TARA_GEWICHT').AsInteger / 1000]));
                        end else begin
                          SetCSVValue ('HazardAmount1', '0.5');
                          SetCSVValue ('HazardAmount[1]', '0.5');
                        end;

                        SetCSVValue ('HazardClass1', '9');  //Benötigt UPS
                        SetCSVValue ('HazardClass[1]', '9');  //Benötigt UPS
                        SetCSVValue ('HazardName1', 'Carbon dioxide, solid');   //Benötigt UPS
                        SetCSVValue ('HazardName[1]', 'Carbon dioxide, solid');   //Benötigt UPS
                      end;
                    end;
                  end;
                end;

                cfgquery.Close;
              end;
            end;

            if CheckCSVValue ('AmazonOrderItemID') then begin
              //Die Amazon Auftrags-Nummer
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add('select * from V_AUFTRAG_ADD_INFOS where REF_AUF_KOPF=:ref');
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              SetCSVValue ('AmazonOrderID', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('KD_AUFTRAG_NR').AsString, ';', ',', [rfReplaceAll])));

              cfgquery.Close;

              //Amazon Artikelnummer und Menge
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add('select * from VQ_AUFTRAG_POS where REF_AUF_KOPF=:ref order by POS_NR');
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              //Die Amazon Artikelnummer
              SetCSVValue ('AmazonOrderItemID', ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('KUNDEN_ARTIKEL_NR').AsString, ';', ',', [rfReplaceAll])));
              SetCSVValue ('AmazonOrderItemQuantity', cfgquery.FieldByName('MENGE_GESAMT').AsString);

              cfgquery.Close;
            end;

            if CheckCSVValue ('Identification') then begin
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add( 'select nvl (prod.OPT_IDENTIFICATION,cfg.OPT_IDENTIFICATION)'
                               +' from V_SPED_CONFIG cfg'
                               +' left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod)'
                               +' where cfg.REF_SPED=:ref_sped'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_prod').Value := query.FieldByName ('REF_SPED_PRODUKT').AsInteger;

              try
                cfgquery.Open;

                opt_ident := cfgquery.Fields [0].AsString;

                cfgquery.Close;
              except
                opt_ident := '';
              end;

              if ((Length (opt_ident) > 0) and (Pos ('1', opt_ident) > 0)) then begin
                //DHL
                SetCSVValue ('Identification', 'true');

                if (copy (opt_ident, 1, 1) = '1') then //IdentificationAdult
                  SetCSVValue ('IdentificationAdult', 'true');

                if (copy (opt_ident, 2, 1) = '1') then //IdentificationByOwnHands
                  SetCSVValue ('IdentificationByOwnHands', 'true');

                if (copy (opt_ident, 3, 1) = '1') then //IdentificationPIN
                  SetCSVValue ('IdentificationPIN', 'true');

                if (copy (opt_ident, 4, 1) = '1') then //IdentificationDateOfBirth
                  SetCSVValue ('IdentificationDateOfBirth', 'true');

                if (copy (opt_ident, 5, 1) = '1') then //IdentificationPassportNumber
                  SetCSVValue ('IdentificationPassportNumber', 'true');

                if (copy (opt_ident, 6, 1) = '1') then //IdentificationPassportType
                  SetCSVValue ('IdentificationPassportType', 'true');

                if (copy (opt_ident, 7, 1) = '1') then //IdentificationFSK
                  SetCSVValue ('IdentificationFSK', 'true');

                //Das ist für UPS +18
                if (copy (opt_ident, 1, 1) = '1') then begin //Signature +  SignatureAdult für UPS
                  SetCSVValue ('Signature', 'true');
                  SetCSVValue ('SignatureAdult', 'true');
                end;
              end;

              cfgquery.Close;
            end;

            if CheckCSVValue ('AdvanceInstructions') then begin
              //DHL Filial Routing
              opt_routing := '';

              //Das Filialrouting kann über das Sped-Produkt oder die Sped-Config gesteuert werden
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add( 'select nvl (prod.OPT_FILIAL_ROUTING,cfg.OPT_FILIAL_ROUTING)'
                               +' from V_SPED_CONFIG cfg'
                               +' left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod)'
                               +' where cfg.REF_SPED=:ref_sped'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_prod').Value := query.FieldByName ('REF_SPED_PRODUKT').AsInteger;

              try
                cfgquery.Open;

                opt_routing := cfgquery.Fields [0].AsString;

                cfgquery.Close;
              except
                opt_routing := '';
              end;

              if (LabelType <> 'Return') then begin
                if (opt_routing = '1') then begin
                  if not liefquery.FieldByName('EMAIL').IsNull then begin
                    //Beim Filialrouting muus die Mail-Adresse nochmals mitgegeben werden.
                    SetCSVValue ('AdvanceInstructions', 'true');
                    SetCSVValue ('AdvanceInstructionsEMail', StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]));
                  end else if not adrquery.FieldByName('EMAIL').IsNull then begin
                    //Beim Filialrouting muus die Mail-Adresse nochmals mitgegeben werden.
                    SetCSVValue ('AdvanceInstructions', 'true');
                    SetCSVValue ('AdvanceInstructionsEMail', StringReplace (adrquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]));
                  end;
                end;
              end;
            end;


            if CheckCSVValue ('SafePlace') or CheckCSVValue ('ReferenceNr') or CheckCSVValue ('ReferenceNr2') then begin
              //Ablageplatz, aktuell nur für GLS

              //Die Ablagestelle kann über die Lieferanschrift oder das Sped-Config vorgegeben werden
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add( 'select auf.AUFTRAG_NR, auftxt.VERSAND_HINWEIS, coalesce (adr.DEPOSIT_PLACE,cfg.DEPOSIT_PLACE)'
                               +' from V_SPED_CONFIG cfg, V_AUFTRAG auf, V_AUFTRAG_ADR adr, V_AUFTRAG_TEXTE auftxt'
                               +' where cfg.REF_SPED=:ref_sped and auf.REF=:ref_auf and auftxt.REF_AUF_KOPF=auf.REF and adr.REF=auf.REF_LIEFER_ADR'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              try
                cfgquery.Open;

                //SetCSVValue ('ReferenceNr', StringReplace (copy (cfgquery.Fields [0].AsString, 1, 35), ';', ',', [rfReplaceAll]));
                //SetCSVValue ('ReferenceNr2', StringReplace (copy (cfgquery.Fields [1].AsString, 1, 35), ';', ',', [rfReplaceAll]));
                SetCSVValue ('SafePlace', StringReplace (cfgquery.Fields [2].AsString, ';', ',', [rfReplaceAll]));

                cfgquery.Close;
              except
              end;
            end;
          finally
            cfgquery.Free;
          end;

          incstr     := '';
          dutaccstr  := '';
          delaccstr  := '';
          sendaccstr := '';
          recvaccstr := '';

          //Incoterms, aktuell nur für GLS
          cfgquery  := TSmartQuery.Create (Nil);

          try
            cfgquery.ReadOnly := True;
            cfgquery.Session := Query.Session;

            cfgquery.SQL.Clear;
            cfgquery.SQL.Add ('select * from V_SPED_GATEWAY_SERVICES'+
                               ' where'+
                               '   ((REF_SPED_GATEWAY is not null and REF_SPED_GATEWAY=:ref_gate) or (REF_SPED is not null and REF_SPED=:ref_sped))'+
                               '   and (LAND_ISO is null or LAND_ISO=:land)'+
                               '   and (REF_SPED_PRODUKTE is null or REF_SPED_PRODUKTE=:ref_prod)'+
                               ' order by REF_SPED_GATEWAY nulls last, REF_SPED nulls last, REF_SPED_PRODUKTE nulls last, LAND_ISO nulls last');
            cfgquery.Params.ParamByName('ref_gate').Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;
            cfgquery.Params.ParamByName('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
            cfgquery.Params.ParamByName('ref_prod').Value := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));
            cfgquery.Params.ParamByName('land').Value := landstr;

            try
              cfgquery.Open;

              if (cfgquery.RecordCount > 0) then begin
                incstr    := cfgquery.FieldByName ('INCOTERM').AsString;
                delaccstr := cfgquery.FieldByName ('CHARGES_ACCOUNT').AsString;
                dutaccstr := cfgquery.FieldByName ('DUTIES_ACCOUNT').AsString;

                if Assigned (cfgquery.FindField ('SENDER_ACCOUNT')) Then
                  sendaccstr := cfgquery.FieldByName ('SENDER_ACCOUNT').AsString;

                if Assigned (cfgquery.FindField ('RECEIVER_ACCOUNT')) Then
                  recvaccstr := cfgquery.FieldByName ('RECEIVER_ACCOUNT').AsString;
              end;

              cfgquery.Close;
            except
            end;

            if (Length (incstr) = 0) then begin
              //Die Incoterms
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add( 'select * from V_SPED_GATEWAY where REF=:ref');
              cfgquery.Params [0].Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;

              try
                cfgquery.Open;

                if Assigned (cfgquery.FindField ('INCOTERM')) then
                  incstr := cfgquery.FieldByName ('INCOTERM').AsString;

                cfgquery.Close;
              except
                incstr := '';
              end;
            end;


            SetCSVValue ('Incoterm', StringReplace (incstr, ';', ',', [rfReplaceAll]));

            SetCSVValue ('CustomsTermsOfShipment', StringReplace (incstr, ';', ',', [rfReplaceAll]));    //CustomsTermsOfShipment
            SetCSVValue ('CustomsPaymentTerms', StringReplace (incstr, ';', ',', [rfReplaceAll]));    //CustomsPaymentTerms
            SetCSVValue ('IncotermBuyerAccount', StringReplace (delaccstr, ';', ',', [rfReplaceAll])); //IncotermBuyerAccount
            SetCSVValue ('IncotermThirdPartySenderAccount', StringReplace (sendaccstr, ';', ',', [rfReplaceAll]));
            SetCSVValue ('IncotermThirdPartyReceiverAccount', StringReplace (recvaccstr, ';', ',', [rfReplaceAll]));

            //Bei Warenpost international müssen auch die Infos für das CN22 bei EU Länder mitgegeben werden
            if not IsLandEU (landstr, query, liefquery.FieldByName ('PLZ').AsString) or ((landstr <> 'DE') and (Versender = 'DHL041')) then begin
              nettosum := 0;

              SetCSVValue ('CustomsInvoiceDate', StringReplace (query.FieldByName ('VERSAND_DATUM').AsString, ';', ',', [rfReplaceAll]));
              SetCSVValue ('CustomsInvoiceSubTotal', Format('%6.3f', [WarenWert]));

              if not (query.FieldByName ('RECHNUNGS_NR').IsNull) then
                csvstr := StringReplace (query.FieldByName ('RECHNUNGS_NR').AsString, ';', ',', [rfReplaceAll])
              else
                csvstr := StringReplace (query.FieldByName ('AUFTRAG_NR').AsString, ';', ',', [rfReplaceAll]);
              SetCSVValue ('CustomsInvoiceNr', csvstr);

            if not (query.FieldByName ('CURRENCY').IsNull) then
              SetCSVValue ('CustomsInvoiceCurrency', query.FieldByName ('CURRENCY').AsString)
            else
              SetCSVValue ('CustomsInvoiceCurrency', 'EUR');

              //Die Zollinfos der Artikel
              cfgquery.SQL.Clear;
              //Alle Bestände auf der NVE, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
              cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN''),bes.MENGE,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                              +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER),nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000'
                              +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                              +',nvl (arset.ANZAHL_VPE, 1) as ANZAHL_VPE'
                              +',pos.REF_AR as POS_REF_AR, ae.REF_AR'
                              +' from'
                              +'   V_LAGER_NVE_BESTAND bes'
                              +'   inner join V_AUFTRAG_POS pos on (pos.REF=bes.REF_AUF_POS)'
                              +'   inner join VQ_AUFTRAG auf on (auf.REF=pos.REF_AUF_KOPF)'
                              +'   inner join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=auf.REF)'
                              +'   inner join V_AUFTRAG_POS_RECHNUNG rep on (rep.REF_AUF_POS=pos.REF)'
                              +'   inner join V_ARTIKEL posar on (posar.REF=pos.REF_AR)'
                              +'   inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF=bes.REF_AR_EINHEIT)'
                              +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                              +'   inner join V_ARTIKEL_VPE vpe on (vpe.REF=ae.REF_EINHEIT)'
                              +'   inner join V_MANDANT_CONFIG mcfg on (mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND))'
                              +'   left outer join V_ARTIKEL_SET arset on (arset.REF=posar.REF_ARTIKEL_SET)'
                              +' where'
                              +'   nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0'
                              +'   and bes.REF_NVE=:ref'
                              +' order by'
                              +'    pos.AUF_POS_NR'
                              );
              cfgquery.Params [0].Value := query.FieldByName('REF_MAIN_NVE').AsInteger;

              cfgquery.Open;

              if (cfgquery.Fields [0].AsString > '0') then begin
                idx := 0;

                while not (cfgquery.Eof) and (res = 0) do begin
                  Inc (idx);

                  if (idx > 25) then
                    break
                  else begin
                    if (cfgquery.Fields [6].IsNull) then begin
                      res := 56;

                      if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                      {$ifdef ResourceText}
                        ErrorText := ErrorText + FormatMessageText (1688, [cfgquery.Fields [1].AsString]);
                      {$else}
                        ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Zolltarifnummer für Artikel %s', [cfgquery.Fields [1].AsString]);
                      {$endif}
                    end;

                    if (cfgquery.Fields [8].IsNull) then begin
                      res := 57;

                      if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                      {$ifdef ResourceText}
                        ErrorText := ErrorText + FormatMessageText (1689, [cfgquery.Fields [1].AsString]);
                      {$else}
                        ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Preis für Artikel %s', [cfgquery.Fields [1].AsString]);
                      {$endif}
                    end;

                    if (res = 0) then begin
                      SetCSVValue ('LineNr'+IntToStr (idx), IntToStr (idx));
                      SetCSVValue ('LineNr['+IntToStr (idx)+']', IntToStr (idx));
                      SetCSVValue ('Articlenumber'+IntToStr (idx), StringReplace (copy (cfgquery.Fields [1].AsString, 1, 32), ';', ',', [rfReplaceAll])); //ArticlenumberX
                      SetCSVValue ('Articlenumber['+IntToStr (idx)+']', StringReplace (copy (cfgquery.Fields [1].AsString, 1, 32), ';', ',', [rfReplaceAll])); //ArticlenumberX
                      SetCSVValue ('Description1'+IntToStr (idx), StringReplace (copy (cfgquery.Fields [2].AsString, 1, 32), ';', ',', [rfReplaceAll])); //Description1X
                      SetCSVValue ('Description1['+IntToStr (idx)+']', StringReplace (copy (cfgquery.Fields [2].AsString, 1, 32), ';', ',', [rfReplaceAll])); //Description1X

                       //PriceX, Betrag ist min. 10 Cent
                      if (cfgquery.Fields [8].IsNull or (cfgquery.Fields [8].AsInteger < 10)) then begin
                        nettosum := nettosum + 0.1;
                        csvstr := Format('%6.3f', [0.1])
                      end else begin
                        if (cfgquery.FieldByName ('ANZAHL_VPE').AsInteger > 1) and (cfgquery.FieldByName ('REF_AR').AsInteger <> cfgquery.FieldByName ('POS_REF_AR').AsInteger) then begin
                          nettosum := nettosum + (cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger);
                          csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000 / cfgquery.FieldByName ('ANZAHL_VPE').AsInteger])
                        end else begin
                          nettosum := nettosum + cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000;
                          csvstr := Format('%6.3f', [cfgquery.FieldByName ('NETTO_BETRAG').AsInteger / 1000]);
                        end;
                      end;
                      SetCSVValue ('Price'+IntToStr (idx), csvstr);
                      SetCSVValue ('Price['+IntToStr (idx)+']', csvstr);

                      if not (query.FieldByName ('CURRENCY').IsNull) then
                        csvstr := query.FieldByName ('CURRENCY').AsString
                      else
                        csvstr := 'EUR';

                      SetCSVValue ('Currency'+IntToStr (idx), StringReplace (csvstr, ';', ',', [rfReplaceAll])); //CurrencyX
                      SetCSVValue ('Currency['+IntToStr (idx)+']', StringReplace (csvstr, ';', ',', [rfReplaceAll])); //CurrencyX
                      SetCSVValue ('Amount'+IntToStr (idx), StringReplace (copy (cfgquery.Fields [3].AsString, 1, 32), ';', ',', [rfReplaceAll])); //AmountX
                      SetCSVValue ('Amount['+IntToStr (idx)+']', StringReplace (copy (cfgquery.Fields [3].AsString, 1, 32), ';', ',', [rfReplaceAll])); //AmountX

                      if (copy (Versender, 1, 3) = 'UPS') then begin
                        SetCSVValue ('AmountUnit'+IntToStr (idx), 'PC');
                        SetCSVValue ('AmountUnit['+IntToStr (idx)+']',  'PC');
                      end else begin
                        SetCSVValue ('AmountUnit'+IntToStr (idx), StringReplace (copy (cfgquery.Fields [4].AsString, 1, 32), ';', ',', [rfReplaceAll]));
                        SetCSVValue ('AmountUnit['+IntToStr (idx)+']', StringReplace (copy (cfgquery.Fields [4].AsString, 1, 32), ';', ',', [rfReplaceAll]));
                      end;

                      //WeightX, Gewicht ist min. 10 Gramm
                      if (cfgquery.Fields [7].IsNull or (cfgquery.Fields [7].AsFloat < 0.01)) then
                        csvstr := '0,01'
                      else
                        csvstr := cfgquery.Fields [7].AsString;

                      SetCSVValue ('Weight'+IntToStr (idx), csvstr);
                      SetCSVValue ('Weight['+IntToStr (idx)+']', csvstr);

                      //OriginCountryX
                      if (cfgquery.Fields [5].IsNull) then
                        csvstr := 'DE'
                      else
                        csvstr := StringReplace (copy (cfgquery.Fields [5].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                      SetCSVValue ('OriginCountry'+IntToStr (idx), csvstr);
                      SetCSVValue ('OriginCountry['+IntToStr (idx)+']', csvstr);

                      if (cfgquery.Fields [6].IsNull) then
                        taricstr := ''
                      else
                        taricstr := StringReplace (copy (cfgquery.Fields [6].AsString, 1, 32), ';', ',', [rfReplaceAll]);
                      SetCSVValue ('CustomsTariffNr'+IntToStr (idx), taricstr);
                      SetCSVValue ('CustomsTariffNr['+IntToStr (idx)+']', taricstr);
                    end;
                  end;

                  cfgquery.Next;
                end;
              end;

              cfgquery.Close;

              if (nettosum > 0) then begin
                SetCSVValue ('ShipmentValue', Format ('%0.3f', [nettosum]));
                SetCSVValue ('CustomsInvoiceSubTotal', Format('%6.3f', [nettosum]));
              end;
            end;
          finally
            cfgquery.Free;
          end;

          if (res = 0) then begin
            //Den letzen gesetzten Wert im Mapping finden
            idx := High (csvwerte);

            while (idx > 0) and (Length (csvwerte [idx]) = 0) do
              Dec (idx);

            //Alle Mappingwerte in eine csv Zeile packen
            csvstr := '';

            for s := 0 to idx do begin
              if (s > 0) then csvstr := csvstr + ';';
              csvstr := csvstr + csvwerte [s];
            end;

            //Die Datei erstmal vollständig erzeugen
            try
              fstream := TFileStream.Create (FileName + '.tmp', fmCreate);
            except
              fstream := Nil;
            end;

            if not Assigned (fstream) then begin
              res := -7;
              ErrorText := 'Fehler beim Erzeugen der SendIT Import-Datei';
            end else begin
              //csvstr := AnsiToUtf8 (csvstr);

              fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

              fstream.Free;

              //Und dann erst freigeben
              RenameFile (FileName + '.tmp', FileName + '.csv');
            end;
          end;
        except
          on  E: Exception do begin
            res := -22;
            ErrorText := 'Fehler beim Mappen der SendIT-Daten';

            {$ifdef ErrorTracking}
              ErrorTrackingModule.WriteErrorLog ('Exception CreateLable', e.ClassName + ' : ' + e.Message);
            {$endif}
          end;
        end;
      end else begin
        try
          strpos := Pos (';', PrtInfo.Leitstand);

          if (strpos > 0) then
            prtname := Copy (PrtInfo.Leitstand, 1, strpos - 1)
          else
            prtname := PrtInfo.Leitstand;

          csvstr := Art + ';' + Versender + ';' + prtname;

          if (Pos ('Return', Versender) > 0) then
            csvstr := csvstr + ';' + '0'
          else if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then
            csvstr := csvstr + ';' + '1'
          else
            csvstr := csvstr + ';' + '0';

          csvstr := csvstr + ';' + Absender;

          if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := csvstr + ';' + query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            csvstr := csvstr + ';' + query.FieldByName('AUF_REFERENZ').AsString
          else csvstr := csvstr + ';' + query.FieldByName('AUFTRAG_NR').AsString;

          //Das wird die ShipmendID
          if (Pos ('REF=KD_KOMM_NR', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            shipid := query.FieldByName('KD_KOMM_NR').AsString
          else if (Pos ('REF=AUF_REFERENZ', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then
            shipid := query.FieldByName('AUF_REFERENZ').AsString
          else if query.FieldByName('AUSLIEFER_NR').IsNull then
            shipid := query.FieldByName('AUFTRAG_NR').AsString
          else
            shipid := query.FieldByName('AUSLIEFER_NR').AsString;

          csvstr := csvstr + ';' + shipid;

          //Bei OPT_MULTI_SHIPMEND=0 ist jedes Packstück eine eigen Sendungen
          if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '0') then begin
            //Die ShipmendID muss hierbei eindeutig sein
            if (Length (shipid) > 14) then
              csvstr := csvstr + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) + 1 - (20 - Length (shipid)))
            else
              csvstr := csvstr + '-' + copy (query.FieldByName('NVE_NR').AsString, Length (query.FieldByName('NVE_NR').AsString) - 5);

            csvstr := csvstr + ';1';
            csvstr := csvstr + ';1';
          end else begin
            //Bei OPT_MULTI_SHIPMEND > 0 pro Auftrag eine Sendungen mit Anzahl Packstücken
            if (Pos ('Return', Versender) > 0) then begin
              csvstr := csvstr + ';1';
              csvstr := csvstr + ';1';
            end else if (true=true) then begin
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                //Über SHIPPING_UNITS oder Anzahl der SKUs die Gesamtanzahl der Packstücke bestimmen
                cfgquery.SQL.Add ('select av.SHIPPING_UNITS, sum (MENGE_GESAMT * nvl (ae.ANZAHL_PACKEINHEIT, 1)) from VQ_AUFTRAG_POS pos, VQ_ARTIKEL_EINHEIT ae, VQ_AUFTRAG_VERSAND av'
                                  +' where av.REF_AUF_KOPF=pos.REF_AUF_KOPF and ae.REF=pos.REF_AR_EINHEIT and pos.REF_AUF_KOPF=:ref group by av.SHIPPING_UNITS'
                                 );
                cfgquery.Params [0].Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if not (cfgquery.Fields [0].IsNull) then
                  sumvpe := cfgquery.Fields [0].AsInteger
                else
                  sumvpe := cfgquery.Fields [1].AsInteger;

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;

              csvstr := csvstr + ';' + query.FieldByName('PACKAGE_NR').AsString;
              csvstr := csvstr + ';' + IntToStr (sumvpe);
            end else begin
              csvstr := csvstr + ';' + query.FieldByName('PACKAGE_NR').AsString;
              csvstr := csvstr + ';' + query.FieldByName('PACKAGE_COUNT').AsString;
            end;
          end;

          if (Length (IDNummer) > 0) then
            csvstr := csvstr + ';' + IDNummer
          else
            csvstr := csvstr + ';' + query.FieldByName('NVE_NR').AsString;

          //Bei DHL ES muss das Gewicht auf 5 kg gesetzt werden, sonst wird eine Bulky-Sendung erzeugt
          if (copy (Versender, 1, 8) = 'DHLSpain') and (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 5.1) then begin
            csvstr := csvstr + ';' + '5.1';
          end else if (query.FieldByName('BRUTTO_GEWICHT').IsNull) then
            csvstr := csvstr + ';' + '2'
          else if (query.FieldByName('BRUTTO_GEWICHT').AsFloat < 0.1) then
            csvstr := csvstr + ';' + '0.100'
          else
            csvstr := csvstr + ';' + Format ('%0.3f', [query.FieldByName('BRUTTO_GEWICHT').AsFloat]);

          if (Pos ('Return', Versender) > 0) then
            csvstr := csvstr + ';;EUR;'
          else begin
            if (WarenWert > 0) then
              csvstr := csvstr + ';' + Format ('%0.3f', [WarenWert])+';EUR'
            else
              csvstr := csvstr + ';;EUR';

            //Nachnahme nur wenn auch ein Wert angegeben ist
            if (WarenWert <= 0) or (query.FieldByName('OPT_NACHNAHME').AsString = '0') then
              csvstr := csvstr + ';'
            else begin
              if ((lowercase (copy (query.FieldByName('SUB_MANDANT').AsString, 1, 5)) = 'leroi') and not query.FieldByName('KUNDEN_NR').IsNull) then
                textstr := ConvertSonderzeichen ('Kundennr: '+query.FieldByName('KUNDEN_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString)
              else if not (query.FieldByName('RECHNUNGS_NR').IsNull) then
                textstr := ConvertSonderzeichen ('Rechnungsnr: '+query.FieldByName('RECHNUNGS_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString)
              else
                textstr := ConvertSonderzeichen ('Auftragsnr: '+query.FieldByName('AUFTRAG_NR').AsString+' vom ' + query.FieldByName('VERSAND_DATUM').AsString + ', '+adrquery.FieldByName('NAME1').AsString+', '+adrquery.FieldByName('ORT').AsString);

              csvstr := csvstr + ';'+textstr;
            end;
          end;

          if (copy (Versender, 1, 3) = 'PFR') then begin
            //Bei Colissimo muss ShipToAdresse1 der Kundennamen udn ShipToAdresse2 die Firma sein
            if (liefquery.FieldByName('COMPANY').IsNull) then begin
              //ShipToAdresse1
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll]));

              //ShipToAdresse2
              if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
                csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))
              else if (Length (liefquery.FieldByName('STRASSE_2').AsString) > 4) then
               //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
                csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
              else
                csvstr := csvstr + ';';
            end else begin
              //ShipToAdresse1, bei uns in dem Falle dann NAME2
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]));

              //ShipToAdresse2
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('COMPANY').AsString, ';', ',', [rfReplaceAll]));
            end;
          end else begin
            //ShipToAdresse1
            csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME1').AsString, ';', ',', [rfReplaceAll]));

            if (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAME2').AsString, ';', ',', [rfReplaceAll]))
            else if (Length (liefquery.FieldByName('STRASSE_2').AsString) > 4) then
             //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse2
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
            else if (copy (Versender, 1, 3) = 'HVS') then
              //Hermes benötig hier immer einen Text
              csvstr := csvstr + ';-'
            else
              csvstr := csvstr + ';';
          end;

          //Bei Hermes gib es keine dritte Adresszeile
          if (copy (Versender, 1, 3) = 'HVS') then
            csvstr := csvstr + ';'
          else begin
            if (Length (liefquery.FieldByName('NAMEZUSATZ').AsString) > 0) then
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NAMEZUSATZ').AsString, ';', ',', [rfReplaceAll]))
            else if (Length (liefquery.FieldByName('STRASSE_2').AsString) > 4) and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              //SendIT kennt keine Strasse_2, daher kommt das ab 4 Zeichen in ShipToAdresse3
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
            else
              csvstr := csvstr + ';';
          end;

          //Strasse und Hausnummer ggf. trennen
          nrstr      := '';
          streetstr := StringReplace (liefquery.FieldByName('STRASSE').AsString, ';', ',', [rfReplaceAll]);

          if (AktSendITIFCHausnummer) then begin
            idx := Length (streetstr);
            while (idx > 1) and (streetstr [idx] <> ' ') do
              Dec (idx);

            if (idx > 1) then begin
              nrstr := copy (streetstr, idx + 1);

              //Bei Hermes kann die Hausnummer maximal 4 Zeichne lange sein, daher dürfen längere Hausnummern nicht abgetrennt werden
              if (copy (Versender, 1, 3) = 'HVS') and (Length (nrstr) > 4) then
                nrstr := ''
              else
                streetstr := Copy (streetstr, 1, idx);
            end;
          end;

          csvstr := csvstr + ';' + ConvertSonderzeichen (streetstr);


          //Zweiter Teil der Strasse
          if not (liefquery.FieldByName('STRASSE_2').IsNull) then begin
            //Kurze Zusätze wie Hausnummer usw. wird an die Strasse angehangen
            if (Length (liefquery.FieldByName('STRASSE_2').AsString) <= 4) then
              csvstr := csvstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]))
            else if (copy (Versender, 1, 3) = 'HVS') and (Length (liefquery.FieldByName('NAME2').AsString) > 0) then
              //Bei Hermes gibt es keinen zweiten Teil der Strasse, somit muss das angehangen werden
              csvstr := csvstr + ' ' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('STRASSE_2').AsString, ';', ',', [rfReplaceAll]));
          end;

          csvstr := csvstr + ';' + StringReplace (liefquery.FieldByName('PLZ').AsString, ';', ',', [rfReplaceAll]);
          csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('ORT').AsString, ';', ',', [rfReplaceAll]));

          if (liefquery.FieldByName('LAND').IsNull) and (liefquery.FieldByName('LAND_ISO').IsNull) then
            csvstr := csvstr + ';DE'
          else if (liefquery.FieldByName('LAND_ISO').IsNull) then
            csvstr := csvstr + ';' + UpperCase (StringReplace (liefquery.FieldByName('LAND').AsString, ';', ',', [rfReplaceAll]))
          else
            csvstr := csvstr + ';' + liefquery.FieldByName('LAND_ISO').AsString;

          if not (kepemail) or liefquery.FieldByName('EMAIL').IsNull then
            csvstr := csvstr + ';' + StringReplace (query.FieldByName('DEFAULT_EMAIL_ADRESS').AsString, ';', ',', [rfReplaceAll])
          else
            csvstr := csvstr + ';' + StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]);

          if (AktSendITIFCImpVersion > 1) then begin
            csvstr := csvstr + ';' + VersandStelle;

            (*Das klappte bei DHL nicht, muss geprüft werden, daher erst mal ohne Abmessungen, wird nur bei DPD - Kleinpaketerkennung - benötigt*)
            if (Pos ('Return', Versender) > 0) then begin
              //Bei Retouren nur die minimale Paketgrösse angeben
              csvstr := csvstr + ';15;15;11';
            end else if (copy (Versender, 1, 3) = 'DPD') then begin
              if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
                if (query.FieldByName('VOLUMEN').IsNull) then
                  csvstr := csvstr + ';;;'
                else begin
                  s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

                  csvstr := csvstr + ';' + IntToStr (s) + ';' + IntToStr (s) + ';' + IntToStr (s);
                end;
              end else begin
                csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('L').AsInteger / 10)) + ';' + IntToStr (Round (query.FieldByName('B').AsInteger / 10)) + ';' + IntToStr (Round (query.FieldByName('h').AsInteger / 10));
              end;
            end else if (query.FieldByName('OPT_DIM_REQUIRED').AsString = '1') then begin
              if (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then begin
                if (query.FieldByName('VOLUMEN').IsNull) then
                  csvstr := csvstr + ';15;15;11'
                else begin
                  s := Round (Power (query.FieldByName('VOLUMEN').AsFloat, 1/3.0) / 10.0);

                  csvstr := csvstr + ';' + IntToStr (s) + ';' + IntToStr (s) + ';' + IntToStr (s);
                end;
              end else if ((query.FieldByName('L').AsInteger * query.FieldByName('B').AsInteger * query.FieldByName('H').AsInteger) < 50*50*50) then begin
                csvstr := csvstr + ';15;15;11'
              end else begin
                csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('L').AsInteger / 10)) + ';' + IntToStr (Round (query.FieldByName('B').AsInteger / 10)) + ';' + IntToStr (Round (query.FieldByName('H').AsInteger / 10));
              end;
            end else if not (query.FieldByName('L').IsNull or query.FieldByName('B').IsNull or query.FieldByName('H').IsNull) then
              if (Versender = 'DHL') then begin
                //Beim DHL Standardpaket darf die Packgrösse nicht kleiner als 15x15x11 cm sein
                if (query.FieldByName('L').AsInteger < 150) then
                  csvstr := csvstr + ';' + '15'
                else
                  csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('L').AsInteger / 10));

                if (query.FieldByName('B').AsInteger < 150) then
                  csvstr := csvstr + ';' + '15'
                else
                  csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('B').AsInteger / 10));

                if (query.FieldByName('H').AsInteger < 110) then
                  csvstr := csvstr + ';' + '11'
                else
                  csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('H').AsInteger / 10));
              end else begin
                csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('L').AsInteger / 10));
                csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('B').AsInteger / 10));
                csvstr := csvstr + ';' + IntToStr (Round (query.FieldByName('H').AsInteger / 10));
              end
            else
              csvstr := csvstr + ';15;15;11';

            if (query.FieldByName('OPT_SPERRGUT').AsString = '1') then begin
              if (query.FieldByName('OPT_SPERRGUT_AS_NORMAL').AsString = '1') then
                csvstr := csvstr + ';0'
              else
                csvstr := csvstr + ';1'
            end else
              csvstr := csvstr + ';0';

            if not (query.FieldByName('EDI_CODE').IsNull) then
              csvstr := csvstr + ';'+query.FieldByName('EDI_CODE').AsString
            else
              csvstr := csvstr + ';KT';

            if not (liefquery.FieldByName('TELEFON').IsNull) then
              csvstr := csvstr + ';'+StringReplace (liefquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
            else if not (adrquery.FieldByName('TELEFON').IsNull) then
              csvstr := csvstr + ';'+StringReplace (adrquery.FieldByName('TELEFON').AsString, ';', ',', [rfReplaceAll])
            else
              csvstr := csvstr + ';'+StringReplace (query.FieldByName('DEFAULT_PHONE_NUMBER').AsString, ';', ',', [rfReplaceAll]);

            if (copy (Versender, 1, 8) = 'DHLSpain') then
               csvstr := csvstr + ';'
            else begin
              if not (liefquery.FieldByName('ANSPRECHPARTNER').IsNull) then begin
                if (copy (Versender, 1, 3) = 'SCH') then
                  csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
                else
                  csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 35), ';', ',', [rfReplaceAll]))
              end else if not (adrquery.FieldByName('ANSPRECHPARTNER').IsNull) then begin
                if (copy (Versender, 1, 3) = 'SCH') then
                  csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (copy (adrquery.FieldByName('ANSPRECHPARTNER').AsString, 1, 30), ';', ',', [rfReplaceAll]))
                else
                  csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (adrquery.FieldByName('ANSPRECHPARTNER').AsString, ';', ',', [rfReplaceAll]))
              end else if not (liefquery.FieldByName('LAND_ISO').IsNull or (liefquery.FieldByName('LAND_ISO').AsString = 'DE')) then
                //Nur bei Export den Empfänger als Ansprechpartner angeben
                csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else if (copy (Versender, 1, 3) = 'SCH') then
                //Ansprechpartner ist bei Schenker Pflicht
                csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (copy (liefquery.FieldByName('NAME1').AsString, 1, 30), ';', ',', [rfReplaceAll]))
              else
                csvstr := csvstr + ';';
            end;

            //Nur bei Export den Sendungsinhalt bezeichnen
            if (liefquery.FieldByName('LAND_ISO').IsNull or (liefquery.FieldByName('LAND_ISO').AsString = 'DE')) then
              csvstr := csvstr + ';'
            else
              csvstr := csvstr + ';' + zolldesc;

            (*
            if (query.FieldByName('IFC_KENNZEICHEN').AsString = 'UPS_18') then begin
              csvstr := csvstr + 'Adult Signature Required';
            end;
            *)

            //Das Gesamtgewicht der Sendung
            if (query.FieldByName('OPT_MULTI_SHIPMEND').AsString = '1') then
              csvstr := csvstr + ';' + query.FieldByName('SENDUNG_BRUTTO').AsString
            else
              csvstr := csvstr + ';' + query.FieldByName('BRUTTO_GEWICHT').AsString
          end;

          if (AktSendITIFCImpVersion > 2) then begin
            if liefquery.FieldByName('NACHNAME').IsNull then begin
              namestr := Trim (liefquery.FieldByName ('NAME1').AsString);

              idx := Length (namestr);

              //Das erste Leerzeichen nach dem Nachnamen finden
              while (idx > 1) and (namestr [idx] <> ' ') do
                Dec (idx);

              if (idx > 1) then begin
                nachnamestr := copy (namestr, idx + 1);

                //Leerzeichen zwischen Vor- und Nachname entfernen
                while (idx > 1) and (namestr [idx] = ' ') do
                  Dec (idx);

                vornamestr := copy (namestr, 1, idx);
              end else begin
                vornamestr  := '';
                nachnamestr := namestr;
              end;

              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (vornamestr, ';', ',', [rfReplaceAll]));
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (nachnamestr, ';', ',', [rfReplaceAll]));
            end else begin
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('VORNAME').AsString, ';', ',', [rfReplaceAll]));
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (liefquery.FieldByName('NACHNAME').AsString, ';', ',', [rfReplaceAll]));
            end;
          end;

          if (AktSendITIFCImpVersion > 3) then begin
            //csvstr := csvstr + ';' + LVSDatenModul.AktUserNumID;
          end;

          if (AktSendITIFCImpVersion > 4) then begin
            if (Pos ('SENDIT_ABSENDER', query.FieldByName('SENDIT_CONFIG').AsString) > 0) then begin
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';

              //Das Land
              if (AktSendITIFCImpSubVersion > 0) then
                csvstr := csvstr + ';';
              //Das Telefon
              if (AktSendITIFCImpSubVersion > 1) then
                csvstr := csvstr + ';';
            end else if not (AktSendITIFCAbsender) or (Pos ('Return', Versender) > 0) then begin
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';

              //Das Land
              if (AktSendITIFCImpSubVersion > 0) then
                csvstr := csvstr + ';';

              //Das Telefon
              if (AktSendITIFCImpSubVersion > 1) then
                csvstr := csvstr + ';';
            end else begin
              //Absender Adresse übergeben
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                //Bei Dropship wird die Traderadresse angezeigt
                cfgquery.SQL.Add (  'select tadr.REF as REF,tadr.NAME1 as NAME1,tadr.NAMEZUSATZ as NAMEZUSATZ,'
                                   +'coalesce (translate (tadr.STRASSE using CHAR_CS),ml.ABSENDER_STRASSE,loc.STRASSE) as STRASSE,'
                                   +'coalesce (tadr.PLZ, ml.ABSENDER_PLZ, loc.PLZ) as PLZ,'
                                   +'coalesce (translate (tadr.ORT using CHAR_CS), ml.ABSENDER_ORT, loc.ORT) as ORT,'
                                   +'coalesce (translate (tadr.LAND using CHAR_CS),loc.LAND,''DE'') as LAND,'
                                   +'coalesce (tadr.TELEFON,loc.TELEFON) as TELEFON'
                                  +' from V_AUFTRAG auf inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                                  +' inner join V_LOCATION_REL_LAGER rel on (rel.REF_LAGER=auf.REF_LAGER) inner join V_LOCATION loc on (loc.REF=rel.REF_LOCATION)'
                                  +' left outer join V_AUFTRAG_ADR tadr on (tadr.REF_AUF_KOPF=auf.REF and tadr.ART=''TRADER_ADR'')'
                                  +' left outer join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                  +' where auf.REF=:ref_auf'
                                  );

                cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if not (cfgquery.FieldByName('REF').IsNull) then begin
                  csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('NAME1').AsString, ';', ',', [rfReplaceAll]));
                  csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('NAMEZUSATZ').AsString, ';', ',', [rfReplaceAll]));
                  csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('STRASSE').AsString, ';', ',', [rfReplaceAll]));
                  csvstr := csvstr + ';' + cfgquery.FieldByName ('PLZ').AsString;
                  csvstr := csvstr + ';' + ConvertSonderzeichen (cfgquery.FieldByName ('ORT').AsString);

                  //Das Land
                  if (AktSendITIFCImpSubVersion > 0) then
                    csvstr := csvstr + ';'+cfgquery.FieldByName ('LAND').AsString;

                  //Das Telefon
                  if (AktSendITIFCImpSubVersion > 1) then
                    csvstr := csvstr + ';'+ConvertSonderzeichen (StringReplace (cfgquery.FieldByName ('TELEFON').AsString, ';', ',', [rfReplaceAll]));
                end else begin
                  //Ansonsten die vom Lager
                  cfgquery.Close;

                  cfgquery.SQL.Clear;

                  cfgquery.SQL.Add ( 'select coalesce (ml.ABSENDER_NAME,m.ADR_ZUSATZ,m.NAME),ml.ABSENDER_ZUSATZ,nvl (ml.ABSENDER_STRASSE,loc.STRASSE),nvl (ml.ABSENDER_PLZ,loc.PLZ),nvl (ml.ABSENDER_ORT,loc.ORT),nvl (loc.LAND,''DE'') as LAND,loc.TELEFON'
                                    +' from V_MANDANT m inner join V_LOCATION loc on (loc.REF=:ref_loc)'
                                    +' inner join V_MANDANT_REL_LOCATION ml on (ml.REF_MAND=m.REF and ml.REF_LOCATION=loc.REF)'
                                    +' where m.REF=:ref_mand'
                                    );

                  cfgquery.Params.ParamByName('ref_loc').Value := query.FieldByName('REF_LOCATION').AsInteger;

                  if query.FieldByName('REF_SUB_MAND').IsNull then
                    cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger
                  else cfgquery.Params.ParamByName('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;

                  cfgquery.Open;

                  csvstr := csvstr + ';' + StringReplace (cfgquery.Fields [0].AsString, ';', ',', [rfReplaceAll]);
                  csvstr := csvstr + ';' + StringReplace (cfgquery.Fields [1].AsString, ';', ',', [rfReplaceAll]);
                  csvstr := csvstr + ';' + StringReplace (cfgquery.Fields [2].AsString, ';', ',', [rfReplaceAll]);
                  csvstr := csvstr + ';' + StringReplace (cfgquery.Fields [3].AsString, ';', ',', [rfReplaceAll]);
                  csvstr := csvstr + ';' + StringReplace (cfgquery.Fields [4].AsString, ';', ',', [rfReplaceAll]);

                  //Das Land
                  if (AktSendITIFCImpSubVersion > 0) then
                    csvstr := csvstr + ';'+cfgquery.Fields [5].AsString;

                  //Das Telefon
                  if (AktSendITIFCImpSubVersion > 1) then
                    csvstr := csvstr + ';'+cfgquery.Fields [6].AsString;
                end;

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;
            end;
          end;

          (* Wird nicht mehr unterstützt
          if (AktSendITIFCImpVersion = 6) then begin
            //Vorausverfügung
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.LockType := ltReadOnly;
              cfgquery.Connection := LVSDatenModul.MainADOConnection;

              cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_SPED_GATEWAY').AsInteger;

              cfgquery.Open;

              if (cfgquery.FieldByName('OPT_DISPOSAL').IsNull) then begin
                csvstr := csvstr + ';';
                csvstr := csvstr + ';';
                csvstr := csvstr + ';';
              end else begin
                optstr := cfgquery.FieldByName('OPT_DISPOSAL').AsString;

                if ((Length (optstr) > 0) and (copy (optstr, 1, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';

                if ((Length (optstr) > 0) and (copy (optstr, 2, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';

                if ((Length (optstr) > 0) and (copy (optstr, 3, 1) = '1')) then
                  csvstr := csvstr + ';1'
                else
                  csvstr := csvstr + ';0';
              end;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;
          *)

          if (AktSendITIFCImpVersion > 6) then begin
            //Auftragsnummer für DB-Schenker usw.
            csvstr := csvstr + ';' + query.FieldByName('AUFTRAG_NR').AsString;
          end;

          //Packetshop-Angaben für Modial (Lillydoo)
          if (AktSendITIFCImpVersion > 7) then begin
            if (Pos ('Return', Versender) > 0) then begin
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
            end else begin
              //Mit Packstation bzw. Paketshop
              cfgquery  := TSmartQuery.Create (Nil);

              try
                cfgquery.ReadOnly := True;
                cfgquery.Session := Query.Session;

                cfgquery.SQL.Add ('select * from V_AUFTRAG_ADR where REF=:ref');
                cfgquery.Params [0].Value := query.FieldByName('REF_LIEFER_ADR').AsInteger;

                cfgquery.Open;

                if (cfgquery.FieldByName('PACKING_STATION').IsNull) then begin
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                end else begin
                  numstr := cfgquery.FieldByName('PACKING_STATION').AsString;

                  if (copy (Versender, 1, 3) = 'MON') then begin
                    while (length (numstr) < 5) do numstr := '0' + numstr;
                    while (length (numstr) > 5) do Delete (numstr, 1, 1);
                  end;

                  csvstr := csvstr + ';true';
                  csvstr := csvstr + ';'+numstr;
                end;

                cfgquery.Close;
              finally
                cfgquery.Free;
              end;
            end;
          end;

          //Lieferscheinnummer und Inhaltsangaben für DHL2Mann (Real) und Amazone Prime (Hellweg)
          if (AktSendITIFCImpVersion > 8) then begin
            //Lieferscheinnummer
            csvstr := csvstr + ';' + query.FieldByName('LIEFERSCHEIN_NR').AsString;       //<map outside="xx" inside="DeliveryNoteNr" />

            //DHL2Mann
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              cfgquery.SQL.Add ('select bes.ARTIKEL_NR,bes.ARTIKEL_TEXT,bes.COLLI_NAME,ap.MENGE_GESAMT from V_NVE_INHALT bes left outer join VQ_AUFTRAG_POS ap on (ap.REF=bes.REF_AUF_POS) where bes.REF_NVE=:ref');
              cfgquery.Params [0].Value := query.FieldByName('REF_NVE').AsInteger;

              cfgquery.Open;

              arnr  := cfgquery.FieldByName('ARTIKEL_NR').AsString;
              aranz := cfgquery.FieldByName('MENGE_GESAMT').AsInteger;

              csvstr := csvstr + ';' + arnr;                                            //<map outside="xx" inside="ArticleNr" />

              if (aranz > 0) then                                                       //<map outside="xx" inside="ArticleQuantity", neu ab 27.02.2020 />
                csvstr := csvstr + ';' + IntToStr (aranz)
              else
                csvstr := csvstr + ';';
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('ARTIKEL_TEXT').AsString, ';', ',', [rfReplaceAll]));    //<map outside="xx" inside="ArticleDescription" />

              if not (cfgquery.FieldByName('COLLI_NAME').IsNull) then
                csvstr := csvstr + ' / ' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('COLLI_NAME').AsString, ';', ',', [rfReplaceAll]));

              cfgquery.Close;

              if (AktSendITIFCExtDHL2Mann) then begin
                versart := '';

                //Verstandart bestimmen
                if (Length (arnr) > 0) then begin
                  cfgquery.SQL.Clear;

                  if (query.FieldByName('REF_SUB_MAND').IsNull) then begin
                    cfgquery.SQL.Add ( 'select nvl (con.CONVERT_VALUE, nvl (ae.VERSAND_ART, ar.VERSAND_ART))'
                                      +' from VQ_ARTIKEL ar'
                                      +' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF_AR=ar.REF)'
                                      +' left outer join VQ_CONVERT_VALUES con on (con.CENVERT_TYP=''FreightTerms'' and con.CENVERT_FROM=''SendIT'' and con.CONVERT_TO=:kenn and con.VALUE=ar.VERSAND_ART)'
                                      +' where ar.REF_MAND=:ref_mand and ar.REF_SUB_MAND is null and ar.ARTIKEL_NR=:ar_nr'
                                      +' order by case when ae.OPT_MASTER=''1'' then 0 else 9 end asc'
                                     );
                    cfgquery.Params.ParamByName ('ref_mand').Value := query.FieldByName('REF_MAND').AsInteger;
                  end else begin
                    cfgquery.SQL.Add ( 'select nvl (con.CONVERT_VALUE, nvl (ae.VERSAND_ART, ar.VERSAND_ART))'
                                      +' from VQ_ARTIKEL ar'
                                      +' inner join VQ_ARTIKEL_EINHEIT ae on (ae.REF_AR=ar.REF)'
                                      +' left outer join VQ_CONVERT_VALUES con on (con.CENVERT_TYP=''FreightTerms'' and con.CENVERT_FROM=''SendIT'' and con.CONVERT_TO=:kenn and con.VALUE=ar.VERSAND_ART)'
                                      +' where ar.REF_SUB_MAND=:ref_mand and ar.ARTIKEL_NR=:ar_nr'
                                      +' order by case when ae.OPT_MASTER=''1'' then 0 else 9 end asc'
                                     );
                    cfgquery.Params.ParamByName ('ref_mand').Value := query.FieldByName('REF_SUB_MAND').AsInteger;
                  end;
                  cfgquery.Params.ParamByName ('ar_nr').Value := arnr;
                  cfgquery.Params.ParamByName ('kenn').Value := query.FieldByName('DFUE_KENNZEICHEN').AsString;

                  cfgquery.Open;

                  versart := cfgquery.Fields[0].AsString;

                  cfgquery.Close;
                end;

                csvstr := csvstr + ';'+versart;                                         //<map outside="xx" inside="FreightTerms" />
                csvstr := csvstr + ';';                                                 //<map outside="xx" inside="ProductType" />
                csvstr := csvstr + ';';                                                 //<map outside="xx" inside="OrderType" />
              end;

              //ShipperCombineID für verladungsbezogene Abschlüsse
              if (AktSendITIFCCombineID) then
                csvstr := csvstr + ';';                                                 //<map outside="xx" inside="ShipperCombineID" />

              //Die Amazon Auftrags-Nummer
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add('select * from V_AUFTRAG_ADD_INFOS where REF_AUF_KOPF=:ref');
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('KD_AUFTRAG_NR').AsString, ';', ',', [rfReplaceAll]));

              cfgquery.Close;

              //Amazon Artikelnummer und Menge
              cfgquery.SQL.Clear;
              cfgquery.SQL.Add('select * from VQ_AUFTRAG_POS where REF_AUF_KOPF=:ref order by POS_NR');
              cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              cfgquery.Open;

              //Die Amazon Artikelnummer
              csvstr := csvstr + ';' + ConvertSonderzeichen (StringReplace (cfgquery.FieldByName('KUNDEN_ARTIKEL_NR').AsString, ';', ',', [rfReplaceAll]));
              csvstr := csvstr + ';' + cfgquery.FieldByName('MENGE_GESAMT').AsString;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;

          if (AktSendITIFCImpVersion > 10) then begin
            //DHL Identification
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              cfgquery.SQL.Add( 'select nvl (prod.OPT_IDENTIFICATION,cfg.OPT_IDENTIFICATION)'
                               +' from V_SPED_CONFIG cfg'
                               +' left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod)'
                               +' where cfg.REF_SPED=:ref_sped'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_prod').Value := query.FieldByName ('REF_SPED_PRODUKT').AsInteger;

              try
                cfgquery.Open;

                opt_ident := cfgquery.Fields [0].AsString;

                cfgquery.Close;
              except
                opt_ident := '';
              end;

              if ((Length (opt_ident) = 0) or (Pos ('1', opt_ident) = 0)) then
                csvstr := csvstr + ';;;;;;;;;;'
              else begin
                //DHL
                csvstr := csvstr + ';true';

                if (copy (opt_ident, 1, 1) = '1') then //IdentificationAdult
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 2, 1) = '1') then //IdentificationByOwnHands
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 3, 1) = '1') then //IdentificationPIN
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 4, 1) = '1') then //IdentificationDateOfBirth
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 5, 1) = '1') then //IdentificationPassportNumber
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 6, 1) = '1') then //IdentificationPassportType
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                if (copy (opt_ident, 7, 1) = '1') then //IdentificationFSK
                  csvstr := csvstr + ';true'
                else csvstr := csvstr + ';';

                //Das ist für UPS +18
                if (copy (opt_ident, 1, 1) = '1') then begin //Signature +  SignatureAdult für UPS
                  csvstr := csvstr + ';true;true';
                end else csvstr := csvstr + ';;';

              end;

              cfgquery.Close;
            finally
              cfgquery.Free;
            end;
          end;

          //Für UPS wird der State-Code für US benötigt.
          if Assigned (liefquery.FindField ('STATE')) and not (liefquery.FieldByName('STATE').IsNull) then
            csvstr := csvstr + ';' + liefquery.FieldByName('STATE').AsString
          else
            csvstr := csvstr + ';';

          //Die Hausnummer ausgeben
          if (Length (nrstr) > 0) then
            csvstr := csvstr + ';' + nrstr
          else
            csvstr := csvstr + ';';

          if AktSendITIFCFilialRouting then begin
            opt_routing := '';

            //DHL Filial Routing
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Das Filialrouting kann über das Sped-Produkt oder die Sped-Config gesteuert werden
              cfgquery.SQL.Add( 'select nvl (prod.OPT_FILIAL_ROUTING,cfg.OPT_FILIAL_ROUTING)'
                               +' from V_SPED_CONFIG cfg'
                               +' left outer join V_SPED_PRODUKTE prod on (prod.REF=:ref_prod)'
                               +' where cfg.REF_SPED=:ref_sped'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_prod').Value := query.FieldByName ('REF_SPED_PRODUKT').AsInteger;

              try
                cfgquery.Open;

                opt_routing := cfgquery.Fields [0].AsString;

                cfgquery.Close;
              except
                opt_routing := '';
              end;
            finally
              cfgquery.Free;
            end;


            if (opt_routing = '1') then begin
              //Beim Filialrouting muus die Mail-Adresse nochmals mitggeben werden.
              csvstr := csvstr + ';true';
              csvstr := csvstr + ';'+StringReplace (liefquery.FieldByName('EMAIL').AsString, ';', ',', [rfReplaceAll]);
            end else begin
              csvstr := csvstr + ';';
              csvstr := csvstr + ';';
            end;
          end;

          if AktSendITIFCAblageOrt then begin
            placestr := '';

            //Ablageplatz, aktuell nur für GLS
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              //Die Ablagestelle kann über die Lieferanschrift oder das Sped-Config vorgegeben werden
              cfgquery.SQL.Add( 'select auftxt.VERSAND_HINWEIS, coalesce (auftxt.VERSAND_HINWEIS,adr.DEPOSIT_PLACE,cfg.DEPOSIT_PLACE)'
                               +' from V_SPED_CONFIG cfg, V_AUFTRAG auf, V_AUFTRAG_ADR adr, V_AUFTRAG_TEXTE auftxt'
                               +' where cfg.REF_SPED=:ref_sped and auf.REF=:ref_auf auftxt.REF_AUF_KOPF=auf.REF and adr.REF=auf.REF_LIEFER_ADR'
                              );
              cfgquery.Params.ParamByName ('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName ('ref_auf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

              try
                cfgquery.Open;

                placestr := cfgquery.Fields [0].AsString;

                cfgquery.Close;
              except
                opt_routing := '';
              end;
            finally
              cfgquery.Free;
            end;

            csvstr := csvstr + ';'+StringReplace (placestr, ';', ',', [rfReplaceAll]);
          end;

          if (AktSendITIFCZoll > 0) then begin
            incstr    := '';
            dutaccstr := '';
            delaccstr := '';

            //Incoterms, aktuell nur für GLS
            cfgquery  := TSmartQuery.Create (Nil);

            try
              cfgquery.ReadOnly := True;
              cfgquery.Session := Query.Session;

              cfgquery.SQL.Clear;
              cfgquery.SQL.Add ('select INCOTERM, CHARGES_ACCOUNT, DUTIES_ACCOUNT from V_SPED_GATEWAY_SERVICES'+
                                 ' where'+
                                 '   ((REF_SPED_GATEWAY is not null and REF_SPED_GATEWAY=:ref_gate) or (REF_SPED is not null and REF_SPED=:ref_sped))'+
                                 '   and (LAND_ISO is null or LAND_ISO=:land)'+
                                 '   and (REF_SPED_PRODUKTE is null or REF_SPED_PRODUKTE=:ref_prod)'+
                                 ' order by REF_SPED_GATEWAY nulls last, REF_SPED nulls last, REF_SPED_PRODUKTE nulls last, LAND_ISO nulls last');
              cfgquery.Params.ParamByName('ref_gate').Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;
              cfgquery.Params.ParamByName('ref_sped').Value := query.FieldByName ('REF_SPED').AsInteger;
              cfgquery.Params.ParamByName('ref_prod').Value := DBGetReferenz (query.FieldByName ('REF_SPED_PRODUKT'));
              cfgquery.Params.ParamByName('land').Value := landstr;

              try
                cfgquery.Open;

                if (cfgquery.RecordCount > 0) then begin
                  incstr    := cfgquery.Fields [0].AsString;
                  delaccstr := cfgquery.Fields [1].AsString;
                  dutaccstr := cfgquery.Fields [2].AsString;
                end;

                cfgquery.Close;
              except
              end;

              if (Length (incstr) = 0) then begin
                //Die Incoterms
                cfgquery.SQL.Clear;
                cfgquery.SQL.Add( 'select * from V_SPED_GATEWAY where REF=:ref');
                cfgquery.Params [0].Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;

                try
                  cfgquery.Open;

                  if Assigned (cfgquery.FindField ('INCOTERM')) then
                    incstr := cfgquery.FieldByName ('INCOTERM').AsString;

                  cfgquery.Close;
                except
                  incstr := '';
                end;
              end;

              (* Geht so nicht, jeder Versender hat da Sonderlocken
              if (Length (incstr) = 0) then
                incstr := 'EXW';
              *)

              csvstr := csvstr + ';'+StringReplace (incstr, ';', ',', [rfReplaceAll]);

              if not IsLandEU (landstr) then begin
                csvstr := csvstr + ';'+StringReplace (incstr, ';', ',', [rfReplaceAll]);    //CustomsTermsOfShipment
                csvstr := csvstr + ';'+StringReplace (incstr, ';', ',', [rfReplaceAll]);    //CustomsPaymentTerms

                if (AktSendITIFCIncoterm > 0) then
                  csvstr := csvstr + ';'+StringReplace (dutaccstr, ';', ',', [rfReplaceAll]); //IncotermBuyerAccount

                csvstr := csvstr + ';'+StringReplace (query.FieldByName ('VERSAND_DATUM').AsString, ';', ',', [rfReplaceAll]);
                csvstr := csvstr + ';'+Format('%6.3f', [WarenWert]);

                if not (query.FieldByName ('RECHNUNGS_NR').IsNull) then
                  csvstr := csvstr + ';'+StringReplace (query.FieldByName ('RECHNUNGS_NR').AsString, ';', ',', [rfReplaceAll])
                else
                  csvstr := csvstr + ';'+StringReplace (query.FieldByName ('AUFTRAG_NR').AsString, ';', ',', [rfReplaceAll]);

                if not (query.FieldByName ('CURRENCY').IsNull) then
                  csvstr := csvstr + ';'+ query.FieldByName ('CURRENCY').AsString
                else
                  csvstr := csvstr + ';'+'EUR';

                //Die Zollinfos der Artikel
                cfgquery.SQL.Clear;
                //Alle Positionen des Auftrags, welche keine Textartikel sind, das Gewicht ist das Artikelgewicht
                cfgquery.SQL.Add('select mcfg.OPT_AUTO_CUSTOMS,ar.ARTIKEL_NR,GETARTIKELTEXT (ar.REF, ''EN''),pos.MENGE_GESAMT,vpe.KURZ_BEZEICHNUNG,ar.COUNTRY_OF_ORIGIN'
                                +',nvl (ar.TARIC_NUMBER, mcfg.BASE_TARIC_NUMBER),nvl(ae.BRUTTO_GEWICHT,ae.NETTO_GEWICHT)/1000'
                                +',nvl (rep.NETTO_BETRAG,(re.NETTO_BETRAG / auf.SUM_VPE)*pos.MENGE_BESTELLT) as NETTO_BETRAG'
                                +' from VQ_AUFTRAG auf, V_AUFTRAG_RECHNUNG re, V_AUFTRAG_POS pos, V_AUFTRAG_POS_RECHNUNG rep, V_ARTIKEL ar, VQ_ARTIKEL_EINHEIT ae, V_ARTIKEL_VPE vpe, V_MANDANT_CONFIG mcfg'
                                +' where pos.REF_AUF_KOPF=auf.REF and ar.REF=pos.REF_AR and ae.REF=pos.REF_AR_EINHEIT and vpe.REF=ae.REF_EINHEIT and mcfg.REF_MAND=nvl (auf.REF_SUB_MAND, auf.REF_MAND)'
                                +' and pos.REF_PARENT_POS is null and re.REF_AUF_KOPF=auf.REF and rep.REF_AUF_POS=pos.REF and nvl (ar.OPT_TEXT_ARTIKEL, ''0'')=''0'' and nvl (pos.MENGE_GESAMT, 0) > 0 and auf.REF=:ref'
                                );
                cfgquery.Params [0].Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;

                cfgquery.Open;

                if (cfgquery.Fields [0].AsString > '0') then begin
                  idx := 0;

                  while not (cfgquery.Eof) and (res = 0) do begin
                    Inc (idx);

                    if (idx > 16) then
                      break
                    else begin
                      if (cfgquery.Fields [6].IsNull) then begin
                        res := 56;

                        if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                        {$ifdef ResourceText}
                          ErrorText := ErrorText + FormatMessageText (1688, [cfgquery.Fields [1].AsString]);
                        {$else}
                          ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Zolltarifnummer für Artikel %s', [cfgquery.Fields [1].AsString]);
                        {$endif}
                      end;

                      if (cfgquery.Fields [8].IsNull) then begin
                        res := 57;

                        if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13;
                        {$ifdef ResourceText}
                          ErrorText := ErrorText + FormatMessageText (1689, [cfgquery.Fields [1].AsString]);
                        {$else}
                          ErrorText := ErrorText + Format ('Export nicht möglich, fehlende Preis für Artikel %s', [cfgquery.Fields [1].AsString]);
                        {$endif}
                      end;

                      if (res = 0) then begin
                        csvstr := csvstr + ';'+IntToStr (idx);
                        csvstr := csvstr + ';'+StringReplace (copy (cfgquery.Fields [1].AsString, 1, 32), ';', ',', [rfReplaceAll]); //ArticlenumberX
                        csvstr := csvstr + ';'+StringReplace (copy (cfgquery.Fields [2].AsString, 1, 32), ';', ',', [rfReplaceAll]); //Description1X

                         //PriceX, Betrag ist min. 10 Cent
                        if (cfgquery.Fields [8].IsNull or (cfgquery.Fields [8].AsInteger < 10)) then
                          csvstr := csvstr + ';'+Format('%6.3f', [0.1])
                        else
                          csvstr := csvstr + ';'+Format('%6.3f', [cfgquery.Fields [8].AsInteger / 1000]);

                        csvstr := csvstr + ';'+StringReplace ('EUR', ';', ',', [rfReplaceAll]); //CurrencyX
                        csvstr := csvstr + ';'+StringReplace (copy (cfgquery.Fields [3].AsString, 1, 32), ';', ',', [rfReplaceAll]); //AmountX
                        csvstr := csvstr + ';'+StringReplace (copy (cfgquery.Fields [4].AsString, 1, 32), ';', ',', [rfReplaceAll]); //AmountUnitX

                        //WeightX, Gewicht ist min. 10 Gramm
                        if (cfgquery.Fields [7].IsNull or (cfgquery.Fields [7].AsFloat < 0.01)) then
                          csvstr := csvstr + ';'+'0,01'
                        else
                        csvstr := csvstr + ';'+cfgquery.Fields [7].AsString;

                        //OriginCountryX
                        if (cfgquery.Fields [5].IsNull) then
                          csvstr := csvstr + ';'+'DE'
                        else
                          csvstr := csvstr + ';'+StringReplace (copy (cfgquery.Fields [5].AsString, 1, 32), ';', ',', [rfReplaceAll]);

                        if (cfgquery.Fields [6].IsNull) then
                          taricstr := ''
                        else
                          taricstr := StringReplace (copy (cfgquery.Fields [6].AsString, 1, 32), ';', ',', [rfReplaceAll]);

                        csvstr := csvstr + ';' + taricstr
                      end;
                    end;

                    cfgquery.Next;
                  end;
                end;

                cfgquery.Close;
              end;
            finally
              cfgquery.Free;
            end;
          end;

          //Die Datei erstmal vollständig erzeugen
          try
            fstream := TFileStream.Create (FileName + '.tmp', fmCreate);
          except
            fstream := Nil;
          end;

          if not Assigned (fstream) then begin
            res := -7;
            ErrorText := 'Fehler beim Erzeugen der CSV Import-Datei';
          end else begin
            //csvstr := AnsiToUtf8 (csvstr);

            fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

            fstream.Free;

            //Und dann erst freigeben
            RenameFile (FileName + '.tmp', FileName + '.csv');
          end;
        except
          on  E: Exception do begin
            res := -22;
            ErrorText := 'Fehler beim Mappen der SendIT-Daten';

            {$ifdef ErrorTracking}
              ErrorTrackingModule.WriteErrorLog ('Exception CreateLable', e.ClassName + ' : ' + e.Message);
            {$endif}
          end;
        end;
      end;
    finally
      spedquery.Free;
      gatequery.Free;
    end;

    Result := res;
  end;

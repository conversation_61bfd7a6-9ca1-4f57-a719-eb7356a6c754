object ABCMainForm: TABCMainForm
  Left = 0
  Top = 0
  Caption = 'ABC-Klassifizierungen'
  ClientHeight = 637
  ClientWidth = 927
  Color = clBtnFace
  Constraints.MinHeight = 500
  Constraints.MinWidth = 850
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnClose = FormClose
  OnCreate = FormCreate
  OnKeyDown = FormKeyDown
  OnShow = FormShow
  TextHeight = 13
  object Splitter1: TSplitter
    Left = 0
    Top = 200
    Width = 927
    Height = 5
    Cursor = crVSplit
    Align = alTop
    ExplicitTop = 250
    ExplicitWidth = 835
  end
  object UpperPanel: TPanel
    Left = 0
    Top = 0
    Width = 927
    Height = 200
    Align = alTop
    BevelOuter = bvNone
    Constraints.MinHeight = 150
    TabOrder = 0
    object HeaderButtonPanel: TPanel
      Left = 777
      Top = 0
      Width = 150
      Height = 200
      Align = alRight
      BevelOuter = bvNone
      Caption = 'PositionButtonPanel'
      ShowCaption = False
      TabOrder = 0
      DesignSize = (
        150
        200)
      object ConfLabel: TLabel
        Left = 6
        Top = 7
        Width = 39
        Height = 14
        Caption = 'Profile'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
      end
      object EditConfButton: TButton
        Left = 6
        Top = 57
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Profil bearbeiten...'
        TabOrder = 0
        OnClick = EditConfButtonClick
      end
      object DeleteConfButton: TButton
        Left = 6
        Top = 88
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Profil l'#246'schen'
        TabOrder = 1
        OnClick = DeleteConfButtonClick
      end
      object NewConfButton: TButton
        Left = 6
        Top = 26
        Width = 140
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Neues Profil...'
        TabOrder = 2
        OnClick = NewConfButtonClick
      end
    end
    object ConfDBGrid: TDBGridPro
      Left = 0
      Top = 0
      Width = 777
      Height = 200
      Align = alClient
      DataSource = ConfDataSource
      Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
      ReadOnly = True
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -12
      BandsFont.Name = 'Segoe UI'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsNormal
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
  end
  object LowerPanel: TPanel
    Left = 0
    Top = 205
    Width = 927
    Height = 393
    Align = alClient
    BevelOuter = bvNone
    Constraints.MinHeight = 300
    TabOrder = 1
    object PositionDBGrid: TDBGridPro
      Left = 0
      Top = 0
      Width = 777
      Height = 393
      Align = alClient
      DataSource = PositionDataSource
      Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgConfirmDelete, dgCancelOnExit, dgMultiSelect, dgTitleClick, dgTitleHotTrack]
      ReadOnly = True
      TabOrder = 0
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Flat = False
      BandsFont.Charset = DEFAULT_CHARSET
      BandsFont.Color = clWindowText
      BandsFont.Height = -12
      BandsFont.Name = 'Segoe UI'
      BandsFont.Style = []
      Groupings = <>
      GridStyle.Style = gsNormal
      GridStyle.OddColor = clWindow
      GridStyle.EvenColor = clWindow
      TitleHeight.PixelCount = 24
      FooterColor = clBtnFace
      ExOptions = [eoDisableDelete, eoDisableInsert, eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
      RegistryKey = 'Software\Scalabium'
      RegistrySection = 'SMDBGrid'
      WidthOfIndicator = 11
      DefaultRowHeight = 17
      ScrollBars = ssHorizontal
      ColCount = 2
      RowCount = 2
    end
    object PositionButtonPanel: TPanel
      Left = 777
      Top = 0
      Width = 150
      Height = 393
      Align = alRight
      BevelOuter = bvNone
      Caption = 'PositionButtonPanel'
      ShowCaption = False
      TabOrder = 1
      object LowerPositionButtonPanel: TPanel
        Left = 0
        Top = 183
        Width = 150
        Height = 210
        Align = alBottom
        BevelOuter = bvLowered
        TabOrder = 0
        DesignSize = (
          150
          210)
        object PositionLabel: TLabel
          Left = 6
          Top = 5
          Width = 65
          Height = 14
          Caption = 'Positionen'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object SetKlasseButton: TButton
          Left = 6
          Top = 84
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Klasse setzen'
          TabOrder = 0
          OnClick = SetKlasseButtonClick
        end
        object SchwellenwerteGroupbox: TGroupBox
          Left = 6
          Top = 119
          Width = 140
          Height = 83
          Anchors = [akTop, akRight]
          Caption = 'Schwellenwerte'
          TabOrder = 1
          object ASchwelleLabel: TLabel
            Left = 7
            Top = 24
            Width = 11
            Height = 13
            Caption = 'A:'
          end
          object BSchwelleLabel: TLabel
            Left = 7
            Top = 43
            Width = 10
            Height = 13
            Caption = 'B:'
          end
          object CSchwelleLabel: TLabel
            Left = 7
            Top = 62
            Width = 11
            Height = 13
            Caption = 'C:'
          end
        end
        object DeleteArtikelButton: TButton
          Left = 6
          Top = 53
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Artikel entfernen'
          TabOrder = 2
          OnClick = DeleteArtikelButtonClick
        end
        object ReevaluateButton: TButton
          Left = 6
          Top = 22
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Neu Berechnen'
          TabOrder = 3
          OnClick = ReevaluateButtonClick
        end
      end
      object UpperPositionButtonPanel: TPanel
        Left = 0
        Top = 0
        Width = 150
        Height = 145
        Align = alTop
        BevelOuter = bvLowered
        Caption = 'UpperPositionButtonPanel'
        ShowCaption = False
        TabOrder = 1
        DesignSize = (
          150
          145)
        object KopfLabel: TLabel
          Left = 6
          Top = 7
          Width = 91
          Height = 14
          Caption = 'Auswertungen'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
        end
        object NewKopfButton: TButton
          Left = 6
          Top = 50
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Neue Auswertung...'
          TabOrder = 0
          OnClick = NewKopfButtonClick
        end
        object DeleteKopfButton: TButton
          Left = 6
          Top = 113
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Auswertung l'#246'schen'
          TabOrder = 1
          OnClick = DeleteKopfButtonClick
        end
        object EditKopfButton: TButton
          Left = 6
          Top = 82
          Width = 140
          Height = 25
          Anchors = [akTop, akRight]
          Caption = 'Auswertung bearbeiten...'
          TabOrder = 2
          OnClick = EditKopfButtonClick
        end
        object KopfComboBox: TComboBoxPro
          Left = 6
          Top = 23
          Width = 140
          Height = 21
          TabOrder = 3
          OnChange = KopfComboBoxChange
        end
      end
    end
  end
  object BottomPanel: TPanel
    Left = 0
    Top = 598
    Width = 927
    Height = 39
    Align = alBottom
    BevelOuter = bvNone
    Caption = 'BottomPanel'
    ShowCaption = False
    TabOrder = 2
    DesignSize = (
      927
      39)
    object CloseButton: TButton
      Left = 783
      Top = 6
      Width = 140
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Schlie'#223'en'
      TabOrder = 0
      OnClick = CloseButtonClick
    end
  end
  object ConfDataSource: TDataSource
    DataSet = ConfQuery
    OnDataChange = ConfDataSourceDataChange
    Left = 520
    Top = 72
  end
  object PositionDataSource: TDataSource
    DataSet = PositionQuery
    Left = 513
    Top = 352
  end
  object ConfQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 608
    Top = 72
  end
  object PositionQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 616
    Top = 352
  end
  object KopfQuery: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 728
    Top = 208
  end
  object BlockedTimer: TTimer
    Interval = 400
    OnTimer = BlockedTimerTimer
    Left = 456
    Top = 328
  end
end

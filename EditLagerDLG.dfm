object EditLagerForm: TEditLagerForm
  Left = 428
  Top = 420
  BorderStyle = bsDialog
  Caption = 'EditLagerForm'
  ClientHeight = 783
  ClientWidth = 509
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    509
    783)
  TextHeight = 13
  object Bevel1: TBevel
    Left = 8
    Top = 743
    Width = 493
    Height = 11
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 654
    ExplicitWidth = 430
  end
  object OkButton: TButton
    Left = 337
    Top = 752
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 4
    ExplicitLeft = 331
    ExplicitTop = 718
  end
  object AbortButton: TButton
    Left = 426
    Top = 752
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 5
    ExplicitLeft = 420
    ExplicitTop = 718
  end
  object LagerPanel: TPanel
    Left = 0
    Top = 0
    Width = 509
    Height = 49
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 0
    ExplicitWidth = 503
    DesignSize = (
      509
      49)
    object Label4: TLabel
      Left = 8
      Top = 8
      Width = 67
      Height = 13
      Caption = 'Niederlassung'
    end
    object LocComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 493
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      TabOrder = 0
      ExplicitWidth = 487
    end
  end
  object MandantPanel: TPanel
    Left = 0
    Top = 49
    Width = 509
    Height = 61
    Align = alTop
    BevelOuter = bvNone
    Caption = 'MandantPanel'
    TabOrder = 1
    ExplicitWidth = 503
    DesignSize = (
      509
      61)
    object Label11: TLabel
      Left = 8
      Top = 7
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Bevel2: TBevel
      Left = 8
      Top = 54
      Width = 493
      Height = 9
      Anchors = [akLeft, akRight]
      Shape = bsTopLine
      ExplicitWidth = 362
    end
    object MandComboBox: TComboBoxPro
      Left = 8
      Top = 24
      Width = 493
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      TabOrder = 0
      Visible = False
      ExplicitWidth = 487
    end
  end
  object DatenPanel: TPanel
    Left = 0
    Top = 110
    Width = 509
    Height = 381
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 2
    ExplicitWidth = 503
    DesignSize = (
      509
      381)
    object Label1: TLabel
      Left = 8
      Top = 4
      Width = 58
      Height = 13
      Caption = 'Lager-Name'
    end
    object Label2: TLabel
      Left = 160
      Top = 4
      Width = 65
      Height = 13
      Caption = 'Beschreibung'
    end
    object Label3: TLabel
      Left = 8
      Top = 98
      Width = 55
      Height = 13
      Caption = 'Betriebs-Nr.'
    end
    object Label9: TLabel
      Left = 108
      Top = 98
      Width = 17
      Height = 13
      Caption = 'ILN'
    end
    object Label10: TLabel
      Left = 248
      Top = 98
      Width = 89
      Height = 13
      Caption = 'EU-Zulassungs-Nr.'
    end
    object Bevel5: TBevel
      Left = 8
      Top = 92
      Width = 493
      Height = 11
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 487
    end
    object Label12: TLabel
      Left = 8
      Top = 46
      Width = 39
      Height = 13
      Caption = 'Lagerart'
    end
    object Label13: TLabel
      Left = 289
      Top = 46
      Width = 110
      Height = 13
      Anchors = [akTop, akRight]
      Caption = 'Zugeh'#246'riges Sperrlager'
      ExplicitLeft = 283
    end
    object Label16: TLabel
      Left = 160
      Top = 46
      Width = 108
      Height = 13
      Caption = 'Standard H'#246'henklasse'
    end
    object Label22: TLabel
      Left = 406
      Top = 98
      Width = 56
      Height = 13
      Caption = 'Interface ID'
    end
    object NameEdit: TEdit
      Left = 8
      Top = 20
      Width = 138
      Height = 21
      MaxLength = 64
      TabOrder = 0
      Text = 'NameEdit'
    end
    object BeschreibungEdit: TEdit
      Left = 160
      Top = 20
      Width = 341
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      MaxLength = 64
      TabOrder = 1
      Text = 'BeschreibungEdit'
      ExplicitWidth = 335
    end
    object BetriebEdit: TEdit
      Left = 8
      Top = 114
      Width = 89
      Height = 21
      MaxLength = 5
      TabOrder = 5
      Text = 'BetriebEdit'
    end
    object ILNEdit: TEdit
      Left = 108
      Top = 114
      Width = 130
      Height = 21
      TabOrder = 6
      Text = 'ILNEdit'
      OnKeyPress = NumEditKeyPress
    end
    object EUNrEdit: TEdit
      Left = 248
      Top = 114
      Width = 145
      Height = 21
      TabOrder = 7
      Text = 'EUNrEdit'
    end
    object PageControl1: TPageControl
      Left = 0
      Top = 162
      Width = 509
      Height = 219
      ActivePage = WETabSheet
      Align = alBottom
      TabOrder = 9
      ExplicitWidth = 503
      object BestandTabSheet: TTabSheet
        Caption = 'Bestand'
        ImageIndex = -1
        object BesMergeCheckBox: TCheckBox
          Left = 3
          Top = 49
          Width = 462
          Height = 17
          Caption = 'Gleichartige Best'#228'nde auf LEs und LPs zusammenf'#252'hren'
          TabOrder = 1
        end
        object BestandCheckBox: TCheckBox
          Left = 3
          Top = 14
          Width = 462
          Height = 17
          Caption = 'Das Lager ist bestandsgef'#252'hrt'
          Checked = True
          State = cbChecked
          TabOrder = 0
        end
      end
      object WETabSheet: TTabSheet
        Caption = 'Warenannahme'
        ImageIndex = 1
        object OptWEImCheckBox: TCheckBox
          Left = 3
          Top = 88
          Width = 382
          Height = 17
          Caption = 'WE f'#252'r Bestellungen direkt nach Abschlu'#223' melden'
          TabOrder = 5
        end
        object OptWEPosAKTCheckBox: TCheckBox
          Left = 3
          Top = 119
          Width = 382
          Height = 17
          Caption = 'WE-Positionen sofort verf'#252'gbar'
          TabOrder = 6
        end
        object AutoCloseBestellungCheckBox: TCheckBox
          Left = 3
          Top = 168
          Width = 382
          Height = 17
          Caption = 'Vollst'#228'ndig belieferte Bestellungen automatisch schlie'#223'en'
          TabOrder = 8
        end
        object OptWEBesAKTBestCloseCheckBox: TCheckBox
          Left = 3
          Top = 139
          Width = 382
          Height = 17
          Caption = 'WE-Best'#228'nde erst nach Bestellabschluss verf'#252'gbar'
          TabOrder = 7
          OnClick = OptWEBesAKTBestCloseCheckBoxClick
        end
        object NoteDateReqCheckBox: TCheckBox
          Left = 3
          Top = 49
          Width = 238
          Height = 17
          Caption = 'Lieferscheindatum ist Pflicht'
          TabOrder = 1
        end
        object NoteDateDefaultCheckBox: TCheckBox
          Left = 263
          Top = 49
          Width = 187
          Height = 17
          Caption = 'Lieferscheindatum vorbelegen'
          TabOrder = 2
        end
        object DeliveryDateReqCheckBox: TCheckBox
          Left = 3
          Top = 29
          Width = 238
          Height = 17
          Caption = 'Anlieferdatum ist Pflicht'
          TabOrder = 3
        end
        object DeliveryDateDefaultCheckBox: TCheckBox
          Left = 263
          Top = 29
          Width = 187
          Height = 17
          Caption = 'Anlieferdatum vorbelegen'
          TabOrder = 4
        end
        object NoteNoDutyCheckBox: TCheckBox
          Left = 3
          Top = 6
          Width = 198
          Height = 17
          Caption = 'Lieferschein-Nummer ist Pflicht'
          TabOrder = 0
        end
      end
      object RetoureTabSheet: TTabSheet
        Caption = 'Retouren'
        ImageIndex = 5
        object AutoCloseRetourenAvisCheckBox: TCheckBox
          Left = 4
          Top = 76
          Width = 382
          Height = 17
          Caption = 'Vollst'#228'ndig belieferte Retourenavise automatisch schlie'#223'en'
          TabOrder = 0
        end
        object OptRETImCheckBox: TCheckBox
          Left = 4
          Top = 16
          Width = 287
          Height = 17
          Caption = 'Retouren direkt nach Abschlu'#223' melden'
          TabOrder = 1
        end
        object OPTRETPosAKTCheckBox: TCheckBox
          Left = 4
          Top = 46
          Width = 287
          Height = 17
          Caption = 'Retourenpositionen sofort verf'#252'gbar'
          TabOrder = 2
        end
        object RetUniqueIDCheckBox: TCheckBox
          Left = 4
          Top = 106
          Width = 462
          Height = 17
          Caption = 'F'#252'r alle Retourenbest'#228'nde jeweils eine eindeutige ID vergeben'
          TabOrder = 3
        end
        object RetNotAKTCheckBox: TCheckBox
          Left = 4
          Top = 136
          Width = 462
          Height = 17
          Caption = 'Retourenbest'#228'nde bei Abschluss nicht aktivieren'
          TabOrder = 4
        end
      end
      object AufPlanTabSheet: TTabSheet
        Caption = 'Auftragsplanung'
        ImageIndex = 4
        object PlanVollPALCheckBox: TCheckBox
          Left = 3
          Top = 8
          Width = 301
          Height = 17
          Caption = 'Vollplattenplanung zul'#228'ssig'
          TabOrder = 0
        end
        object PlanNVEPalFaktorCheckBox: TCheckBox
          Left = 3
          Top = 24
          Width = 301
          Height = 17
          Caption = 'NVE-Bildung '#252'ber Plattenfaktor zul'#228'ssig'
          TabOrder = 1
        end
        object KommOptCheckBox: TCheckBox
          Left = 3
          Top = 40
          Width = 289
          Height = 17
          Caption = 'Kommissionierverdichtung f'#252'r St'#252'ckartikel zul'#228'ssig'
          TabOrder = 2
        end
        object SumResCheckBox: TCheckBox
          Left = 3
          Top = 72
          Width = 289
          Height = 17
          Caption = 'Nur Summenreservierung'
          TabOrder = 3
        end
        object PlanTourPalCheckBox: TCheckBox
          Left = 3
          Top = 120
          Width = 378
          Height = 17
          Caption = 'Komm-Planung nach PAL-Faktor (nur Sped-Touren)'
          TabOrder = 4
        end
      end
      object KommTabSheet: TTabSheet
        Caption = 'Kommissionierung'
        object Label23: TLabel
          Left = 295
          Top = 75
          Width = 44
          Height = 13
          Caption = 'Einheiten'
        end
        object Label24: TLabel
          Left = 247
          Top = 56
          Width = 122
          Height = 13
          Caption = 'Maximale Korrekturmenge'
        end
        object Label25: TLabel
          Left = 247
          Top = 8
          Width = 116
          Height = 13
          Caption = 'Nahe-Null-Inventur unter'
        end
        object Label26: TLabel
          Left = 295
          Top = 27
          Width = 44
          Height = 13
          Caption = 'Einheiten'
        end
        object ErsatzpickCheckBox: TCheckBox
          Left = 3
          Top = 117
          Width = 214
          Height = 17
          Caption = 'Ersatzpick bei Fehlware'
          TabOrder = 0
        end
        object FehlwareCheckInvCheckBox: TCheckBox
          Left = 3
          Top = 87
          Width = 166
          Height = 17
          Caption = 'Platzinventur bei Fehlware'
          TabOrder = 1
        end
        object NullInvCheckBox: TCheckBox
          Left = 3
          Top = 9
          Width = 205
          Height = 17
          Caption = 'Nulldurchgangs-Inventur'
          TabOrder = 2
        end
        object FehlwareInvCheckBox: TCheckBox
          Left = 3
          Top = 59
          Width = 166
          Height = 17
          Caption = 'Inventur bei Fehlware'
          TabOrder = 3
          OnClick = FehlwareInvCheckBoxClick
        end
        object MaxInvMengeEdit: TEdit
          Left = 247
          Top = 72
          Width = 38
          Height = 21
          MaxLength = 3
          TabOrder = 5
          Text = 'MaxInvMengeEdit'
          OnKeyPress = NumEditKeyPress
        end
        object NaheNullEdit: TEdit
          Left = 247
          Top = 24
          Width = 38
          Height = 21
          MaxLength = 3
          TabOrder = 6
          Text = 'NaheNullEdit'
          OnKeyPress = NumEditKeyPress
        end
        object BesCheckKommBeginCheckBox: TCheckBox
          Left = 3
          Top = 149
          Width = 214
          Height = 17
          Caption = 'Bestandscheck bei Komm-Beginn'
          TabOrder = 4
        end
        object AutoCheckInvCheckBox: TCheckBox
          Left = 247
          Top = 99
          Width = 229
          Height = 17
          Caption = 'Bei '#220'berschreitung eine Inventur anlegen'
          TabOrder = 7
          OnClick = AutoCheckInvCheckBoxClick
        end
        object AutoInvArCheckBox: TCheckBox
          Left = 258
          Top = 122
          Width = 219
          Height = 17
          Caption = 'Nur den betreffenden Artikel z'#228'hlen'
          TabOrder = 8
        end
      end
      object WATabSheet: TTabSheet
        Caption = 'Warenausgang'
        ImageIndex = 2
        object OptCloseLFCheckBox: TCheckBox
          Left = 3
          Top = 8
          Width = 270
          Height = 17
          Caption = 'Lieferungen automatisch abschlie'#223'en'
          TabOrder = 0
        end
        object OptVerkaufCheckBox: TCheckBox
          Left = 3
          Top = 24
          Width = 270
          Height = 17
          Caption = 'Direktverkauf zul'#228'ssig'
          TabOrder = 1
        end
        object LiefRetCheckBox: TCheckBox
          Left = 3
          Top = 40
          Width = 258
          Height = 17
          Caption = 'Lieferantenretouren zul'#228'ssig'
          TabOrder = 2
        end
      end
      object QSTabSheet: TTabSheet
        Caption = 'Qualit'#228'tskontrolle'
        ImageIndex = 3
        TabVisible = False
        object Label19: TLabel
          Left = 3
          Top = 13
          Width = 45
          Height = 13
          Caption = 'Annahme'
        end
        object Label20: TLabel
          Left = 3
          Top = 40
          Width = 38
          Height = 13
          Caption = 'Retoure'
        end
        object QSWEComboBox: TComboBoxPro
          Left = 63
          Top = 10
          Width = 145
          Height = 21
          TabOrder = 0
          Text = 'QSWEComboBox'
        end
        object QSRETComboBox: TComboBoxPro
          Left = 63
          Top = 37
          Width = 145
          Height = 21
          TabOrder = 1
          Text = 'QSRETComboBox'
        end
      end
    end
    object ArtComboBox: TComboBoxPro
      Left = 8
      Top = 62
      Width = 144
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 2
      OnChange = ArtComboBoxChange
      ExplicitWidth = 138
    end
    object SperrLagerComboBox: TComboBoxPro
      Left = 289
      Top = 62
      Width = 212
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 4
      ExplicitLeft = 283
    end
    object HKLComboBox: TComboBoxPro
      Left = 160
      Top = 62
      Width = 117
      Height = 21
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      ColWidth = 80
      ItemHeight = 15
      TabOrder = 3
      ExplicitWidth = 111
    end
    object IFCIDEdit: TEdit
      Left = 406
      Top = 114
      Width = 89
      Height = 21
      MaxLength = 5
      TabOrder = 8
      Text = 'IFCIDEdit'
      OnKeyPress = NumEditKeyPress
    end
  end
  object AdressPanel: TPanel
    Left = 0
    Top = 491
    Width = 509
    Height = 224
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 3
    ExplicitWidth = 503
    DesignSize = (
      509
      224)
    object Label5: TLabel
      Left = 8
      Top = 8
      Width = 62
      Height = 13
      Caption = 'Adresszusatz'
    end
    object Label6: TLabel
      Left = 8
      Top = 50
      Width = 108
      Height = 13
      Caption = 'Strasse / Hausnummer'
    end
    object Label7: TLabel
      Left = 8
      Top = 92
      Width = 53
      Height = 13
      Caption = 'Postleitzahl'
    end
    object Label14: TLabel
      Left = 8
      Top = 142
      Width = 36
      Height = 13
      Caption = 'Telefon'
    end
    object Bevel3: TBevel
      Left = 8
      Top = 136
      Width = 493
      Height = 9
      Anchors = [akLeft, akTop, akRight]
      Shape = bsTopLine
      ExplicitWidth = 430
    end
    object Label18: TLabel
      Left = 8
      Top = 186
      Width = 25
      Height = 13
      Caption = 'Email'
    end
    object Label15: TLabel
      Left = 129
      Top = 142
      Width = 17
      Height = 13
      Caption = 'Fax'
    end
    object Label8: TLabel
      Left = 96
      Top = 92
      Width = 14
      Height = 13
      Caption = 'Ort'
    end
    object Label21: TLabel
      Left = 397
      Top = 92
      Width = 24
      Height = 13
      Caption = 'Land'
    end
    object Label17: TLabel
      Left = 248
      Top = 142
      Width = 78
      Height = 13
      Caption = 'Ansprechpartner'
    end
    object AdrEdit: TEdit
      Left = 8
      Top = 24
      Width = 493
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 0
      Text = 'AdrEdit'
      ExplicitWidth = 487
    end
    object RoadEdit: TEdit
      Left = 8
      Top = 66
      Width = 493
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 1
      Text = 'Edit1'
      ExplicitWidth = 487
    end
    object PLZEdit: TEdit
      Left = 8
      Top = 108
      Width = 65
      Height = 21
      MaxLength = 8
      TabOrder = 2
      Text = 'PLZEdit'
    end
    object FonEdit: TEdit
      Left = 8
      Top = 158
      Width = 100
      Height = 21
      MaxLength = 32
      TabOrder = 5
      Text = 'FonEdit'
    end
    object MailEdit: TEdit
      Left = 8
      Top = 202
      Width = 487
      Height = 21
      MaxLength = 64
      TabOrder = 8
      Text = 'MailEdit'
    end
    object FaxEdit: TEdit
      Left = 129
      Top = 158
      Width = 100
      Height = 21
      MaxLength = 32
      TabOrder = 6
      Text = 'FaxEdit'
    end
    object OrtEdit: TEdit
      Left = 96
      Top = 108
      Width = 293
      Height = 21
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 3
      Text = 'OrtEdit'
      ExplicitWidth = 287
    end
    object LandEdit: TEdit
      Left = 403
      Top = 108
      Width = 98
      Height = 21
      Anchors = [akTop, akRight]
      TabOrder = 4
      Text = 'LandEdit'
      ExplicitLeft = 397
    end
    object ContactEdit: TEdit
      Left = 248
      Top = 158
      Width = 247
      Height = 21
      MaxLength = 64
      TabOrder = 7
      Text = 'ContactEdit'
    end
  end
end

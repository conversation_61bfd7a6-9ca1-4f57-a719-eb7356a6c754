unit ChromiumDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ComCtrls, AdvCustomControl, AdvWebBrowser, Generics.Collections, DatenModul;

type
  TApp = (
    appIdentityHub
  );

  TChromiumForm = class(TForm)
    WebBrowser: TAdvWebBrowser;
    StatusBar: TStatusBar;
    procedure FormCreate(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure WebBrowserInitialized(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    AppCookieMap: TDictionary<TApp, TArray<TAdvWebBrowserCookie>>;
    isWebBrowserInitialized: boolean;
    FCurrentApp: TApp;
    procedure SetCurrentApp(const Value: TApp);
    function GetCurrentApp: TApp;
    procedure ApplyCookies;
    procedure DefineCookies;
    function CreateCookie(const Name, Value, Domain, Path: string; Expires: TDateTime): TAdvWebBrowserCookie;
  public
    property CurrentApp: TApp read GetCurrentApp write SetCurrentApp;
  end;

implementation

{$R *.dfm}

uses
  ShellAPI, ConfigModul;

function TChromiumForm.CreateCookie(const Name, Value, Domain, Path: string; Expires: TDateTime): TAdvWebBrowserCookie;
begin
  Result.Name := Name;
  Result.Value := Value;
  Result.Domain := Domain;
  Result.Path := Path;
  Result.Expires := Expires;
  Result.Secure := True;
  Result.SameSite := sstLax;
end;

procedure TChromiumForm.DefineCookies;
begin
  AppCookieMap.Clear;

  AppCookieMap.Add(appIdentityHub, [
    CreateCookie('username', LVSDatenModul.DBUser, 'localhost', '/', Now + 1),
    CreateCookie('password', LVSDatenModul.DBPasswd, 'localhost', '/', Now + 1),
    CreateCookie('schema', LVSDatenModul.Schema, 'localhost', '/', Now + 1),
    CreateCookie('server', LVSDatenModul.DataSource, 'localhost', '/', Now + 1),
    CreateCookie('userPrefix', LVSConfigModul.DBUserPrefix, 'localhost', '/', Now + 1)
  ]);


end;

procedure TChromiumForm.SetCurrentApp(const Value: TApp);
begin
  FCurrentApp := Value;
  if isWebBrowserInitialized then begin
    ApplyCookies;
  end;
end;

function TChromiumForm.GetCurrentApp: TApp;
begin
  Result := FCurrentApp;
end;

procedure TChromiumForm.ApplyCookies;
var
  Cookies: TArray<TAdvWebBrowserCookie>;
  Cookie: TAdvWebBrowserCookie;
begin
  if not isWebBrowserInitialized then begin
    Exit;
  end;

  if not AppCookieMap.TryGetValue(FCurrentApp, Cookies) then begin
    Exit;
  end;

  for Cookie in Cookies do
  begin
    WebBrowser.AddCookie(Cookie);
  end;
end;

procedure TChromiumForm.FormCreate(Sender: TObject);
begin
  isWebBrowserInitialized := False;
  AppCookieMap := TDictionary<TApp, TArray<TAdvWebBrowserCookie>>.Create;

  DefineCookies;

  FCurrentApp := appIdentityHub;
end;

procedure TChromiumForm.FormDestroy(Sender: TObject);
begin
  AppCookieMap.Free;
end;

procedure TChromiumForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LVSConfigModul.SaveFormInfo(Self);
end;

procedure TChromiumForm.WebBrowserInitialized(Sender: TObject);
begin
  isWebBrowserInitialized := True;
  ApplyCookies;
end;

(*
procedure TChromiumForm.WebBrowserBeforeDownload(Sender: TObject;
  const browser: ICefBrowser; const downloadItem: ICefDownloadItem;
  const suggestedName: ustring; const callback: ICefBeforeDownloadCallback);
var
  fname   : String;
  ctmpstr : array [0..MAX_PATH] of Char;
begin
  GetTempPath(MAX_PATH, @ctmpstr);

  fname := StrPas (ctmpstr) + suggestedName;

  callback.Cont(fname, True);
end;

procedure TChromiumForm.WebBrowserDownloadUpdated(Sender: TObject;
  const browser: ICefBrowser; const downloadItem: ICefDownloadItem;
  const callback: ICefDownloadItemCallback);
var
 dwres : DWORD;
begin
  if downloadItem.IsInProgress then
    StatusBar.SimpleText := IntToStr(downloadItem.PercentComplete) + '%'
  else begin
    StatusBar.SimpleText := '';

    if (ExtractFileExt(downloadItem.FullPath) = '.pdf') then begin
      dwres := ShellExecute (0, nil, PChar(downloadItem.FullPath), nil, nil, SW_SHOWNORMAL);

      if dwres <= 32 then
    end;
  end;
end;

procedure TChromiumForm.WebBrowserLoadEnd(Sender: TObject;
  const browser: ICefBrowser; const frame: ICefFrame; httpStatusCode: Integer);
begin
  Cursor := crDefault;
end;

procedure TChromiumForm.WebBrowserLoadError(Sender: TObject;
  const browser: ICefBrowser; const frame: ICefFrame; errorCode: Integer;
  const errorText, failedUrl: ustring);
begin
  Cursor := crDefault;
end;

procedure TChromiumForm.WebBrowserLoadStart(Sender: TObject; const browser: ICefBrowser; const frame: ICefFrame);
begin
  Cursor := crHourGlass;
end;

procedure TChromiumForm.WebBrowserStatusMessage(Sender: TObject;
  const browser: ICefBrowser; const value: ustring);
begin
  StatusBar.SimpleText := value
end;

procedure TChromiumForm.WebBrowserTitleChange(Sender: TObject;
  const browser: ICefBrowser; const title: ustring);
begin
  Caption := title;
end;
*)

initialization
  //CefBrowserSubprocessPath := 'cromelib\wow_helper.exe';

end.
